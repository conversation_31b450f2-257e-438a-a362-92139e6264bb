# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-06-14 00:54:45 - Log of updates made.

*

## Coding Patterns

* **Full Give Condition Validation Pattern**: 满赠活动条件校验模式
  - 后端校验：在MCart模型中实现validateFullGiveConditions方法，支持金额满赠和数量满赠的详细条件验证
  - 购物车更新校验：在updateBuyNumApi方法中集成validateCartFullGiveConditions，实时验证满赠条件
  - 订单提交校验：在confirmationOfOrder方法中添加validateOrderFullGiveConditions，防止绕过前端限制
  - 错误信息优化：返回具体的缺少数量或金额信息，便于前端展示详细提示
  - 前端状态管理：通过hasItemFullGiveCondition和getFullGiveConditionDetail方法实现赠品可选状态的实时判断
  - UI禁用状态：使用disabled-btn样式类和条件渲染实现赠品不可选的视觉反馈
  - 实时更新机制：通过refreshFullGiveInfo方法在购物车变化时自动更新满赠状态

* **Automatic Gift Conversion Pattern**: 自动赠品转换模式
  - 自动状态更新：购物车变化时自动检测不满足条件的赠品，无需用户选择直接转换
  - 自动转换机制：系统自动将无效赠品转换为按原价购买的正常商品，更新sourceType字段
  - 透明提示：自动转换后给用户明确提示，告知哪些商品已转换及价格影响
  - 状态同步：前后端实时同步赠品状态，确保数据一致性
  - 批量处理：支持批量自动处理多个无效赠品，提高操作效率
  - 简化UI：移除复杂的用户选择界面，简化赠品显示和操作流程

* **Request Deduplication and Debounce Pattern**: 请求去重和防抖模式
  - 请求去重：使用Map缓存正在进行的请求，相同参数的请求只发送一次
  - 防抖机制：短时间内的多次操作只执行最后一次，减少不必要的API调用
  - 状态管理：使用标志位控制更新状态，避免watch监听器的重复触发
  - 参数验证：完整的参数验证和边界值检查，避免无效请求
  - 资源清理：页面销毁时清理定时器和请求缓存，防止内存泄漏
  - 错误处理：完善的异常处理机制，确保请求失败不影响用户体验

* **Individual SKU Validation Pattern**: 独立SKU校验模式
  - 业务逻辑：每个目标SKU必须单独满足其最低购买数量要求，而非总和计算
  - 数据结构：支持targetSkuIds配置格式，包含skuId和requiredQuantity字段
  - 校验方法：validateIndividualSkuQuantities逐个检查每个SKU的购买数量
  - 错误信息：提供具体的商品名称和缺少数量，如"商品A 还需购买2件"
  - 倍数计算：基于所有SKU中满足条件的最小倍数进行赠品发放
  - 兼容性：保持向后兼容，支持简化配置格式和混合格式

* **Dual Validation Pattern**: 双重校验模式
  - 前端校验：实时检查用户操作的合法性，提供即时反馈和良好的用户体验
  - 后端校验：提供最终的安全校验，防止恶意绕过前端限制
  - API设计：validateFullGiveCondition接口返回详细的校验结果和错误信息
  - 错误处理：前后端使用一致的错误信息格式，确保用户体验的连贯性
  - 安全考虑：即使前端被绕过，后端仍能保证数据的完整性和业务规则的正确执行

* **Cart Full Give Integration Pattern**: 购物车满赠活动集成模式
  - 参数处理：在Controller层commonFieldFilter方法中添加fullGiveId参数处理
  - 数据验证：在Model层添加满赠活动有效性验证（存在性、状态、时间有效性）
  - 数据存储：在extends字段中以JSON格式存储满赠活动ID，格式为{"fullGiveId": 123}
  - 数据区分：通过hash值区分不同满赠活动的相同商品，确保独立管理
  - 向后兼容：支持单个满赠活动ID格式，为未来多活动支持预留扩展性
  - 错误处理：详细的验证错误信息返回，便于前端处理和用户理解

* **Documentation Maintenance Pattern**: 文档维护和重复内容清理模式
  - 系统性分析：扫描所有文档目录，了解文档组织结构、文件类型和内容概况
  - 内容重复检测：使用文本相似度分析识别重复或高度相似内容（>80%相似度）
  - 多维度决策：基于文件名规范性、修改时间、配置引用、内容质量进行保留决策
  - 配置验证：检查导航配置文件确保所有链接指向保留的文件
  - 清理执行：安全删除冗余文件，保留最优质的文档版本
  - 维护规范：建立文档创建规范、内容管理流程、质量保证措施和后续维护计划

* **Advanced Trend Analysis Pattern**: 高级趋势分析功能开发模式
  - 多维度数据查询：支持amount/count/supplier/efficiency四个维度的趋势分析
  - 性能优化策略：实现缓存机制、SQL查询优化、索引建议文档
  - 前端图表集成：ECharts多维度图表、交互功能、响应式设计
  - 用户体验优化：图表缩放拖拽、悬停提示、用户偏好保存、错误处理
  - API设计模式：扩展现有统计接口、保持向后兼容、灵活参数配置
  - 数据预测功能：基于线性回归的简单预测、置信度计算、预测数据展示
  - 对比分析功能：同比环比计算、趋势百分比、多期数据对比展示

* **Data Flow Fix Pattern**: 数据流向修复和重复数据消除模式
  - 问题识别：通过技术文档分析识别数据流向逻辑错误和重复数据创建问题
  - 修复策略：移除重复插入操作，改为批量更新原有记录的状态和关联信息
  - 分步处理：先执行核心状态更新，再处理额外信息回写，确保主流程不受影响
  - 批量优化：使用DAO层直接执行批量更新，提高性能并减少数据库操作
  - 错误隔离：回写失败不影响主流程，只记录警告日志
  - 兼容性维护：确保方法签名修改后所有调用点正常工作
  - 日志增强：添加详细的操作日志，便于问题追踪和性能监控

* **Model Layer Merge and Cleanup Pattern**: Model层功能合并和彻底清理模式
  - 功能整合策略：将功能重叠的Model类合并到主要业务类中，避免代码冗余
  - 兼容性方法添加：在主类中添加兼容性方法，保持与原有接口的一致性
  - 安全删除流程：验证功能完全整合后，系统性地删除冗余类和更新所有引用点
  - 引用点迁移：更新Controller、测试脚本、文档中的所有引用，确保无遗漏
  - 方法调用优化：重复功能的方法通过内部调用现有方法实现，确保业务逻辑一致性
  - 架构简化验证：通过测试验证确保删除后系统功能完整，架构更加简洁

* **Data Access Layer Refactoring Pattern**: 数据访问层重构和功能整合模式
  - 分阶段重构：先扩展目标类功能 → 创建兼容层 → 修改引用 → 数据库兼容性处理 → 数据迁移 → 测试验证
  - 向后兼容保证：通过兼容层类重定向所有调用，确保现有代码不受影响
  - 字段映射处理：在数据迁移时处理字段名称差异（如orderAmount → totalPrice）
  - 渐进式清理：先保留旧类和旧表，确认系统稳定后再清理
  - 完整文档支持：提供详细的迁移指南、测试计划和回滚方案

* **Element UI Table Pattern**: Use el-table instead of vxe-table for data tables
  - Basic structure: `<el-table :data="tableData" border>`
  - Column definition: `<el-table-column prop="field" label="Title">`
  - Template syntax: `<template slot-scope="scope">` with `scope.row`
  - Fixed columns: `fixed="right"` for operation columns
  - Status switches: Use el-switch with proper event handling
  - Pagination: Use FooterPage component with standard props

* **Settlement Detail Pattern**: 分账明细创建和规则副本保存模式
  - 使用 MSettlementCalculate 进行分账金额计算
  - 保存完整的分账规则副本到 ruleSnapshot 字段（JSON格式）
  - 包含审计信息：规则ID、规则内容、快照时间、快照原因
  - 简化分账类型：仅支持固定金额类型(4)，提高系统可维护性
  - 错误处理：计算失败时使用默认值并记录日志

* **Settlement Type Simplification Pattern**: 结算类型简化模式
  - 统一使用 SETTLEMENT_TYPE_FIXED_AMOUNT = 4 作为唯一结算类型
  - 前端显示逻辑完全简化：所有结算类型相关显示统一为"固定金额"
  - 前端方法简化：getRuleTypeText() 和 getRuleTypeTag() 方法移除复杂逻辑，直接返回固定值
  - 后端计算逻辑专注于固定金额计算：分账金额 = 订单金额 - 固定服务费
  - 数据库字段默认值设为4，注释明确说明为唯一支持类型
  - 用户界面一致性：列表页、详情页、表单页面统一显示逻辑
  - 彻底移除比例和佣金相关显示：移除"结算比例"、"佣金金额"、"平均佣金率"等列
  - 统计页面简化：移除佣金相关图表和统计卡片，专注于销售额和结算金额展示
  - 字段简化：移除冗余的"实际金额"字段，避免数据不一致和维护复杂度

## Architectural Patterns

### 多租户小程序管理模式
* **核心表设计**: 使用qianniao_enterprise_miniprogram作为企业小程序关系管理表
* **数据隔离**: 通过enterpriseId + miniprogramId复合键确保数据隔离和访问控制
* **默认机制**: 支持isDefault字段标识默认小程序，确保向后兼容性
* **缓存策略**: 多级缓存设计，支持企业维度和小程序维度的配置缓存
* **扩展字段**: 使用JSON extends字段支持灵活的配置扩展

### 渐进式架构迁移模式
* **分阶段实施**: 数据库→后端→前端→小程序端→集成测试→上线部署
* **向后兼容**: 保留原有API接口，通过适配层确保现有功能正常
* **数据迁移**: 完整的迁移脚本和回滚方案，确保数据安全
* **风险控制**: 详细的风险评估和防范措施，包含技术、业务、运维、安全四个维度

## Testing Patterns

*   