# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-06-14 00:54:45 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

*   

## Key Features

* **后端自动赠品转换功能**: 实现完全后端自动化的满赠活动赠品转换机制，在购物车数据获取时自动检查满赠条件并执行转换，前端无感知处理，实现真正的自动化赠品状态管理，包括实时条件检查、自动状态转换、内存数据库双重更新，确保用户获得始终正确的商品状态和价格
* **满赠活动赠品转换功能**: 实现完整的满赠活动赠品转换体系，支持将正常商品转换为满赠赠品，包括商品来源类型更新（sourceType: 1→2）、扩展信息设置（保存满赠活动ID）、智能状态管理（支持活动切换和状态恢复），确保满赠活动功能的完整性和双向转换能力
* **购物车赠品状态管理**: 实现完整的购物车赠品状态管理功能，包括自动状态更新机制（当购物车变化导致不满足满赠条件时自动处理赠品）和价格转换机制（支持将免费赠品转换为按原价购买的正常商品），提升用户体验和业务逻辑准确性
* **满赠活动条件校验和UI状态管理**: 实现完整的满赠活动前后端校验逻辑，包括购物车商品数量不满足条件时赠品不可选状态、详细的错误提示信息、实时状态更新机制，确保前后端校验逻辑一致，防止绕过前端限制的恶意请求
* **购物车满赠活动支持**: 支持小程序端用户选择赠品后在购物车extends字段中保存满赠活动ID，实现多满赠活动的独立管理和数据追溯
* **库存出库结算详情**: 支持分账金额计算、分账类型识别和完整分账规则副本保存，确保历史数据的审计追溯能力
* **供应商分账明细功能**: 为供应商角色端提供透明的分账明细查询、详情查看和规则快照展示功能，支持权限控制和数据安全隔离
* **供应商结算统计趋势分析**: 提供多维度数据展示（结算金额、订单数量、供应商参与度、结算效率）、ECharts交互图表、性能优化缓存机制、响应式设计和用户体验优化，支持同比环比对比和趋势预测功能
* **供应商角色端帮助文档体系**: 为供应商角色端开发项目创建完整的帮助文档体系，包括9个核心文档模块，涵盖概述、功能使用、常见问题等，确保供应商能够快速上手和有效使用系统
* **帮助文档可视化增强**: 使用Playwright工具对系统界面进行截图，完善帮助文档的可视化内容，包括商品分类管理、订单管理、库存管理等核心功能模块的界面截图和操作指南，提供真实的界面展示和详细的操作说明
* **系统界面文档化**: 通过Playwright自动化工具获取升辉ERP系统的真实界面截图，包括商品管理主界面、代客下单界面、出库管理、入库管理、库存查询等核心功能模块，为帮助文档提供准确的界面展示和详细的功能说明

## Overall Architecture

### 微信小程序管理架构
* **多小程序支持**: 系统支持一个企业管理多个微信小程序，每个小程序可以绑定不同的默认店铺
* **数据库架构**: 新增qianniao_enterprise_miniprogram表作为核心管理表，扩展现有表结构支持多小程序维度
* **缓存机制**: SystemCache类支持多小程序配置缓存，提供高性能的小程序信息查询
* **前端管理**: 提供完整的小程序管理界面，支持增删改查、默认设置、模板管理等功能
* **小程序端适配**: 支持动态extConfig配置，实现多小程序的灵活部署和管理
* **微信开放平台集成**: 支持批量操作，包括代码提交、审核、发布等流程

[2025-07-25 09:56:30] - New feature: 完善帮助文档内容，使用Playwright工具截取界面图片并更新文档引用
[2025-07-23 12:28:53] - New feature: 完善帮助文档内容，使用Playwright工具截图并更新文档
[2025-07-21 22:00:55] - New feature: 完成供应商角色端帮助文档体系建设，创建9个核心文档模块
[2025-07-02 08:09:24] - Architecture update: 创建企业绑定多个微信小程序功能扩展的完整技术方案文档
[2025-06-16 01:47:57] - New feature: 在库存出库结算详情功能中添加分账金额、分账类型字段和完整分账规则副本保存功能
[2025-06-18 05:01:39] - New feature: 为供应商角色端系统添加分账明细功能模块详细实施计划到任务文档
[2025-06-27 00:51:23] - New feature: 完善供应商结算统计页面中的趋势分析功能，包括多维度数据展示、交互功能、性能优化和用户体验提升
[2025-08-22 22:13:40] - New feature: 实现后端自动赠品转换功能，在购物车数据获取时自动检查满赠条件并执行转换，前端无感知处理，实现真正的自动化赠品状态管理
[2025-08-22 21:49:10] - New feature: 实现满赠活动赠品转换功能，支持将正常商品转换为满赠赠品，包括状态更新、扩展信息设置和智能状态管理
[2025-08-22 18:19:40] - New feature: 实现购物车赠品状态管理功能，包括自动状态更新和价格转换机制，提升用户体验和业务逻辑准确性
[2025-08-22 16:45:48] - New feature: 完成满赠活动模块功能完善，实现前后端校验逻辑和UI状态管理
[2025-08-19 00:20:09] - New feature: 完成购物车满赠活动ID保存功能，支持小程序端选择赠品后在extends字段中保存满赠活动ID
[2025-07-29 23:41:59] - New feature: 使用Playwright工具完善帮助文档，添加系统界面截图和详细说明