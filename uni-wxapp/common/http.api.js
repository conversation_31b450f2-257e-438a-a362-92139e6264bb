// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作，更多内容详见uView对拦截器的介绍部分：
// https://uviewui.com/js/http.html#%E4%BD%95%E8%B0%93%E8%AF%B7%E6%B1%82%E6%8B%A6%E6%88%AA%EF%BC%9F
const install = (Vue, vm) => {
	// 将各个定义的接口名称，统一放进对象挂载到vm.$u.api(因为vm就是this，也即this.$u.api)下
	vm.$u.api = {
		// 根据系统模板d获取对应模板设置内容
		getSettingDataByMessageId: (params = '') => vm.$u.post(
			`/System/ApiPushMessage/getSettingDataByMessageIds`, params),
		// 获取app版本
		getVersion: (params = '') => vm.$u.post(`/System/ApiBasicSetup/getVersion`, params),

		// App微信授权登录获取unionID
		unionIDUserinfo: (params = '') => vm.$u.post(`/Common/Login/userinfo`, params),
		// 获取网页版access_token type 1 公众号  2开放平台 {"code":"","type":2}
		getAccessToken: (params = '') => vm.$u.post(`/Common/Login/access_token`, params),
		// 判断订单支付状态
		orderquery: (params = '') => vm.$u.post(`/Common/Pay/orderquery`, params),

		// 字节跳动获取分享小程序码
		byteCreateQRCode: (params = '') => vm.$u.post(`/Common/Login/byteCreateQRCode`, params),

		// 获取分享微信小程序码
		createwxaqrcode: (params = '') => vm.$u.post(`/common/login/createwxaqrcode`, params),

		// 获取基础资料
		getCustomerInfo: (params = '') => vm.$u.get(`/Customer/ApiCustomer/getCustomerInfo`, params),

		//基础资料
		editCustomerInfo: (params = '') => vm.$u.post(`/Customer/ApiCustomer/editCustomerInfo`, params),
		//首页模版及数据
		getTemplate: (params = '') => vm.$u.get(`/System/ApiTemplate/getTemplate`, params),
		//首页（万能模版）
		getHome: (params = '') => vm.$u.get(`/System/ApiPage/home`, params),
		//首页模版及数据2
		homePage: (params = '') => vm.$u.post(`/System/ApiTemplate/homePage`, params),

		//首页模版及数据2
		getDetailById: (id, params = '') => vm.$u.get(`/System/ApiPage/getDetailById/${id}`, params),
		/**
		 * 地区
		 * */
		// 获取省份
		getAllProvince: () => vm.$u.get(`/Common/SysAreaChina/getAllProvince`),
		// 获取省下的市
		getAllCityByProvinceCode: (code = '') => vm.$u.get(
			`/Common/SysAreaChina/getAllCityByProvinceCode/${code}`),
		// 获取市下的区
		getAllAreaByCityCode: (code = '') => vm.$u.get(`/Common/SysAreaChina/getAllAreaByCityCode/${code}`),
		/**
		 * 生成七牛上传凭证
		 * */
		uploadToken: (params = '') => vm.$u.post(`/Common/Upload/uploadToken`, params),
		UploadDel: (params = '') => vm.$u.post(`/Common/Upload/delete`, params),
		// 短信登录
		smsLogin: (params = '') => vm.$u.post(`/UserCenter/ApiUserCenter/smsLogin`, params),
		// 用户登录
		login: (params = '') =>
			vm.$u.post(`/UserCenter/ApiUserCenter/login`, params),

		//用户注册
		addUserCenter: (params = '') =>
			vm.$u.post(`/UserCenter/ApiUserCenter/register`, params),

		// 发送验证码
		sendMobileCode: (params = '') =>
			vm.$u.post(`/Common/SmsCode/sendMobileCode`, params),

		// 判断用户是否注册过
		mobileIsRegister: (id, params = '') =>
			vm.$u.get(`/UserCenter/ApiUserCenter/mobileIsRegister/${id}`, params),

		// 忘记密码
		forgetPassword: (params = '') =>
			vm.$u.post(`/UserCenter/ApiUserCenter/changePassword`, params),

		// 小程序绑定手机号
		appletsBindMobile: (params = '') =>
			vm.$u.post(`/UserCenter/ApiUserCenter/appletsBindMobile`, params),

		// 根据openID进行登录 ApiUser/User/getUserInfoByOpenId
		getOpenId: (params = '') =>
			vm.$u.post(`/Common/Login/login`, params),

		// 小程序判断是否已绑定 UserCenter/ApiUserCenter/checkBindMiniProgram  post
		checkBindMiniProgram: (params = '') =>
			vm.$u.post(`/UserCenter/ApiUserCenter/checkBindMiniProgram`, params),

		//  小程序解密接口 Common/Login/decryptData
		decryptData: (params = '') =>
			vm.$u.post(`/Common/Login/decryptData`, params),

		//  头条小程序登陆
		microappLogin: (params = '') =>
			vm.$u.post(`/Common/Login/microappLogin`, params),

		/*-------------分类-----------*/
		// 分类模版详情
		getClassSettingInfo: (params = '') =>
			vm.$u.post(`/System/ApiClassSetting/getClassSettingInfo`, params),

		// 商品列表 Goods/Goods/search post
		goodSearch: (params = '') =>
			vm.$u.post(`/Goods/Goods/search`, params),

		// 商品列表 Goods/ApiGoods/getGoodsByCategory  post
		getGoodsByCategory: (params = {}) =>
			vm.$u.post(`/Goods/ApiGoods/getGoodsByCategory`, {...params, isSku: true, showSale: true}),

		// 商品详情
		getGoodsDetail: (id, params = '') =>
			vm.$u.get(`/Goods/ApiGoods/getGoodsDetail/${id}`, params),

		// 分类列表  GoodsCategory/ApiGoodsCategory/getAllCategory get
		getAllCategory: (params = '') =>
			vm.$u.post(`/GoodsCategory/ApiGoodsCategory/getAllCategory`, params),

		// 子分类  GoodsCategory/ApiGoodsCategory/getAllCategoryByPid/1
		getAllCategoryByPidid: (id, params = '') =>
			vm.$u.get(`/GoodsCategory/ApiGoodsCategory/getAllCategoryByPid/${id}`, params),

		// 所有一级分类  GoodsCategory/ApiGoodsCategory/getAllCategoryByPid  get
		getAllCategoryByPid: (params = '') =>
			vm.$u.get(`/GoodsCategory/ApiGoodsCategory/getAllCategoryByPid`, params),
		// 小程序服务列表
		getAllGoodsSupport: (params = '') =>
			vm.$u.post(`/Goods/apiGoods/getAllGoodsSupport`, params),
		// 小程序品牌列表
		getAllBrand: (params = '') =>
			vm.$u.post(`/Goods/apiGoods/getAllBrand`, params),
		/*-------------常购清单------------*/
		// 收藏/取消收藏
		collectGoods: (id) => vm.$u.get(`/Goods/ApiGoods/collect/${id}`),

		// 常购清单
		normalList: (params = '') =>
			vm.$u.post(`/Goods/ApiGoods/normalList`, params),

		/*-------------购物车------------*/
		// 加入购物车  Cart/ApiCart/addCart post
		addCart: (params = '') =>
			vm.$u.post(`/Cart/ApiCart/addCart`, params),

		// 获取购物车 Cart/ApiCart/getCartByUserCenterId get
		getCartByUserCenterId: (params = '') =>
			vm.$u.get(`/Cart/ApiCart/getCartByUserCenterId`, params),

		// 移除购物车  Cart/ApiCart/delCart  post
		delCart: (params = '') =>
			vm.$u.post(`/Cart/ApiCart/delCart`, params),

		// 购物车商品选中/取消
		updateSelection: (params = '') =>
			vm.$u.post(`/Cart/ApiCart/updateSelection`, params),

		// 更新购物车某个商品数量  Cart/ApiCart/updateBuyNum/5 put
		updateBuyNum: (id, params = '') =>
			vm.$u.put(`/Cart/ApiCart/updateBuyNum/${id}`, params),

		// 确认订单
		confirmationOfOrder: (params = '') =>
			vm.$u.post(`/Cart/ApiCart/confirmationOfOrder`, params),
		// 组合商品立即购买
		buyNow: (params = '') =>
			vm.$u.post(`/Cart/ApiCart/buyNow`, params),

		// 商品立即购买
		goodsBuyNow: (params = '') =>
			vm.$u.post(`/cart/apiCart/goodsBuyNow`, params),

		// 验证满赠活动条件
		validateFullGiveCondition: (params = '') =>
			vm.$u.post(`/Cart/ApiCart/validateFullGiveCondition`, params),

		// 将满赠赠品转换为正常商品
		convertGiftToNormalGoods: (params = '') =>
			vm.$u.post(`/Cart/ApiCart/convertGiftToNormalGoods`, params),

		// 验证购物车中所有满赠活动条件
		validateCartFullGiveConditions: (params = '') =>
			vm.$u.post(`/Cart/ApiCart/validateCartFullGiveConditions`, params),

		// 批量处理不满足条件的满赠赠品
		handleInvalidGifts: (params = '') =>
			vm.$u.post(`/Cart/ApiCart/handleInvalidGifts`, params),

		// 将正常商品转换为满赠赠品
		convertNormalGoodsToGift: (params = '') =>
			vm.$u.post(`/Cart/ApiCart/convertNormalGoodsToGift`, params),

		// 批量处理满赠活动赠品转换
		handleGiftConversion: (params = '') =>
			vm.$u.post(`/Cart/ApiCart/handleGiftConversion`, params),
		/*----------地址管理---------------*/
		// 客户地址添加  Customer/ShippingAddress/addShippingAddress post
		addShippingAddress: (params = '') =>
			vm.$u.post(`/Customer/ShippingAddress/addShippingAddress`, params),

		// 客户地址删除 Customer/ShippingAddress/deleteShippingAddress/1 del
		deleteShippingAddress: (id, params = '') =>
			vm.$u.delete(`/Customer/ShippingAddress/deleteShippingAddress/${id}`, params),

		// 客户地址修改 Customer/ShippingAddress/updateShippingAddress put
		updateShippingAddress: (id, params = '') =>
			vm.$u.put(`/Customer/ShippingAddress/updateShippingAddress/${id}`, params),

		// 客户地址详情 Customer/ShippingAddress/getShippingAddressInfo/1 get
		getShippingAddressInfo: (id, params = '') =>
			vm.$u.get(`/Customer/ShippingAddress/getShippingAddressInfo/${id}`, params),

		// 客户类型
		getCustomerSourceList: (params = '') =>
			vm.$u.get(`/Customer/ApiCustomer/getCustomerSourceList`, params),

		// 客户余额明细记录
		getAllCustomerBalanceDetail: (params = '') =>
			vm.$u.post(`/Customer/apiCustomer/getAllCustomerBalanceDetail`, params),

		// 地址
		getAllShippingAddress: (params = '') =>
			vm.$u.post(`/Customer/ShippingAddress/getAllShippingAddress`, params),

		/*---------客户订单---------*/
		// 用户订单状态数量统计
		getOrderStatusNum: (params = '') =>
			vm.$u.post(`/Order/ApiOrder/getOrderStatusNum`, params),
		// 提交银行打款凭证
		addOrderBankData: (params = '') =>
			vm.$u.post(`/Order/ApiOrder/addOrderBankData`, params),
		// 售后订单列表
		getAllOrderReturn: (params = '') =>
			vm.$u.post(`/Order/ApiOrder/getAllOrderReturn`, params),

		// 订单退货添加
		addOrderReturn: (params = '') =>
			vm.$u.post(`/Order/ApiOrder/addOrderReturn`, params),

		// 订单物流查询
		getExpressInfoByOrderId: (id, params = '') =>
			vm.$u.post(`/Order/ApiOrder/getExpressInfoByOrderId/${id}`, params),

		// 订单
		getOrderSelect: (params = '') =>
			vm.$u.post(`/Order/ApiOrder/getOrderSelect`, params),

		// 添加订单
		addOrder: (params = '') =>
			vm.$u.post(`/Order/ApiOrder/addOrder`, params),

		// 获取订单详情
		getOrderInfoById: (id, params = '') =>
			vm.$u.get(`/Order/ApiOrder/getOrderInfoById/${id}`, params),

		// 取消订单&&确认收货
		updateOrderStatus: (id, params = '') =>
			vm.$u.put(`/Order/ApiOrder/updateOrderStatus/${id}`, params),

		// 支付方式列表
		getPaymentType: (params = '') =>
			vm.$u.post(`/System/ApiPayment/getPaymentType`, params),

		// 订单再次支付  Order/ApiOrder/payOrder  POST
		payOrder: (params = '') =>
			vm.$u.post(`/Order/ApiOrder/payOrder`, params),

		//商品列表页调整购买数量 Cart/ApiCart/updateCartNum/1  put
		updateCartNum: (id, params = '') =>
			vm.$u.put(`/Cart/ApiCart/updateCartNum/${id}`, params),

		// 获取用户基本设置  System/ApiBasicSetup/getBasicField  get
		getBasicField: (params = '') =>
			vm.$u.post(`/System/ApiBasicSetup/getBasicField`, params),

		// 商品收藏 Customer/ApiCustomer/collect/19
		collect: (id, params = '') =>
			vm.$u.get(`/Customer/ApiCustomer/collect/${id}`, params),

		/*---------优惠券---------*/
		// 优惠券列表(注册,在线支付,主动领取)
		couponList: (params = '') =>
			vm.$u.post(`/Market/ApiCoupon/couponList`, params),

		// 领取优惠券
		receive: (params = '') =>
			vm.$u.post(`/Market/ApiCoupon/receive`, params),

		// 优惠券盒子
		couponAll: (params = '') =>
			vm.$u.post(`/Market/ApiCoupon/selectAll`, params),

		// 获取秒杀列表 Market/ApiActivity/getActivityAll
		getActivityAll: (params = '') =>
			vm.$u.get(`/Market/ApiActivity/getActivityAll`, params),

		// 企业详情
		getEnterpriseInfo: (params = '') =>
			vm.$u.get(`/Enterprise/ApiEnterprise/getEnterpriseInfo`, params),

		// 会员卡列表 Market/ApiVipCard/getAllVipCard
		getAllVipCard: (params = '') =>
			vm.$u.post(`/Market/ApiVipCard/getAllVipCard`, params),

		// 我的会员卡 Market/ApiVipCard/getMyVipCards
		getMyVipCards: (params = '') =>
			vm.$u.post(`/Market/ApiVipCard/getMyVipCards`, params),

		// 会员卡详情 Market/ApiVipCard/getVipInfo/1
		getVipInfo: (id, params = '') =>
			vm.$u.get(`/Market/ApiVipCard/getVipInfo/${id}`, params),

		// 创建会员卡订单 Market/ApiVipCard/createVipCardOrder
		createVipCardOrder: (params = '') =>
			vm.$u.post(`/Market/ApiVipCard/createVipCardOrder`, params),

		// 获取公司的基本设置
		getBasicSetup: (params = '') =>
			vm.$u.get(`/System/ApiBasicSetup/getBasicField`, params),

		// 一键领取优惠券(会员卡) Market/ApiCoupon/oneKeyReceive
		oneKeyReceive: (params = '') =>
			vm.$u.post(`/Market/ApiCoupon/oneKeyReceive`, params),

		// 订单列表获取我的会员卡 Cart/ApiCart/getVipCard
		orderVipCard: (params = '') =>
			vm.$u.get(`/Cart/ApiCart/getVipCard`, params),

		/* 分销中心 */
		// 分销商申请
		applicationBusinessman: (params = '') =>
			vm.$u.post(`/Commission/ApiCommissionBusinessman/applicationBusinessman`, params),

		// 分销商详情
		getInfoBusinessman: (params = '') =>
			vm.$u.get(`/Commission/ApiCommissionBusinessman/getInfoBusinessman`, params),

		// 查询设置
		getDistributionSetting: (params = '') =>
			vm.$u.post(`/Commission/ApiCommissionSetting/getSetting`, params),

		// 添加上下级关系
		relationshipBusinessman: (params = '') =>
			vm.$u.post(`/Commission/ApiCommissionBusinessman/relationshipBusinessman`, params),

		// 查询文字设置
		getTxtSetting: (params = '') =>
			vm.$u.post(`/Commission/ApiCommissionSetting/getTxtSetting`, params),

		// 查询分销商的下级
		getAllSub: (params = '') =>
			vm.$u.post(`/Commission/ApiCommissionBusinessman/getAllSub`, params),

		// 查询升级商品
		getBuyGoods: (params = '') =>
			vm.$u.get(`/Commission/ApiCommissionSetting/getBuyGoods`, params),

		// 分销中心订单
		getCommissionOrder: (params = '') =>
			vm.$u.post(`/Commission/ApiCommissionBusinessman/getCommissionOrder`, params),
		// 分销订单详情
		getCommissionOrderInfo: (id, params = '') =>
			vm.$u.post(`/Commission/ApiCommissionBusinessman/getCommissionOrderInfo/${id}`, params),
		// 申请提现
		WithdrawalsAdd: (params = '') =>
			vm.$u.post(`/Commission/ApiCommissionWithdrawals/add`, params),
		// 获取提现申请列表
		CommissionWithdrawalsGetAll: (params = '') =>
			vm.$u.post(`/Commission/ApiCommissionWithdrawals/getAll`, params),
		// 获取提现申请详情
		CommissionWithdrawalsDetail: (id, params = '') =>
			vm.$u.get(`/Commission/ApiCommissionWithdrawals/detail/${id}`, params),
		// 等级说明
		getAllGrade: (params = '') =>
			vm.$u.get(`/Commission/ApiCommissionBusinessman/getAllGrade`, params),
		// 排行榜
		commissionTop: (params = '') =>
			vm.$u.get(`/Commission/ApiCommissionBusinessman/commissionTop`, params),
		// 获取设置(小程序)
		MerchantGetSeting: (params = '') =>
			vm.$u.get(`/Merchant/ApiMerchant/getSetting`, params),
		// 商户入驻申请(小程序)
		MerchantApply: (params = '') =>
			vm.$u.post(`/Merchant/ApiMerchant/addApply`, params),
		// 入驻申请修改(小程序)
		updateApply: (id, params = '') =>
			vm.$u.put(`/Merchant/ApiMerchant/updateApply/${id}`, params),
		// 取消入驻申请(小程序)
		delApply: (id, params = '') =>
			vm.$u.get(`/Merchant/ApiMerchant/delApply/${id}`, params),
		// 商户详情(小程序)
		getInfoMerchant: (params = '') =>
			vm.$u.get(`/Merchant/ApiMerchant/getInfoMerchant`, params),
		// 商户列表
		getAllApiMerchant: (params = '') => vm.$u.post(`/Merchant/ApiMerchant/getAll`, params),
		// 获取店铺页面
		shopHome: (params = '') =>
			vm.$u.post(`/System/ApiTemplate/shopHome`, params),
		// 商户设置
		getSettingMerchant: (params = '') => vm.$u.post(`/Merchant/Merchant/getSetting`, params),
		/*------组合套餐------*/
		// 组合套餐列表
		listsApiComBinPackage: (params = '') => vm.$u.post(`/Market/ApiComBinPackage/lists`, params),
		// 组合套餐详情
		detailsApiComBinPackage: (id, params = '') => vm.$u.get(`/Market/ApiComBinPackage/details/${id}`,
			params),
		/*------需求提报------*/
		// 新增需求提报
		addCustomerdemand: (params = '') => vm.$u.post(`/Customer/ApiCustomer/addCustomerdemand`, params),
		/*------余额管理------*/
		// 修改余额支付密码
		updatePayPassword: (params = '') => vm.$u.post(`/Customer/ApiCustomer/updatePayPassword`, params),
		// 添加余额支付密码
		addpayPassword: (params = '') => vm.$u.post(`/Customer/ApiCustomer/addpayPassword`, params),
		// 校验余额支付密码
		checkPayPassword: (params = '') => vm.$u.post(`/Customer/ApiCustomer/checkPayPassword`, params),
		// 余额明细
		getAllMemberBalanceDetail: (params = '') => vm.$u.post(
			`/Customer/apiCustomer/getAllMemberBalanceDetail`, params),
		/*------积分------*/
		// 积分商品详情
		getIntegralGoodsInfo: (id, params = '') => vm.$u.get(
			`/Integral/ApiIntegralGoods/getIntegralGoodsInfo/${id}`, params),
		// 积分商品兑换列表
		getAllIntegralGoodsExchange: (params = '') => vm.$u.post(
			`/Integral/ApiIntegralGoods/getAllIntegralGoodsExchange`, params),
		// 积分流水
		getAllCustomerIntegralDesc: (params = '') => vm.$u.post(
			`/Customer/ApiCustomer/getAllCustomerIntegralDesc`, params),
		// 积分商品列表
		getAllIntegralGoods: (params = '') => vm.$u.post(`/Integral/ApiIntegralGoods/getAllIntegralGoods`,
			params),
		// 积分兑换商品
		addIntegralGoodsExchange: (params = '') => vm.$u.post(
			`/Integral/ApiIntegralGoods/addIntegralGoodsExchange`, params),
		// 积分商品兑换详情
		getIntegralGoodsExchangeInfo: (id, params = '') => vm.$u.get(
			`/Integral/ApiIntegralGoods/getIntegralGoodsExchangeInfo/${id}`, params),
		/*------提现------*/
		// 新增提现
		addReflectDetail: (params = '') => vm.$u.post(`/Customer/apiCustomer/addReflectDetail`, params),
		// 获取所有提现记录
		getAllReflectDetail: (params = '') => vm.$u.post(`/Customer/apiCustomer/getAllReflectDetail`, params),
		/*------满赠------*/
		// 获取可用的满赠活动及赠品
		getAvailableGifts: (params = {}) => vm.$u.post(`/Market/FullGive/getAvailableGifts`, params),
		// 选择满赠赠品
		selectFullGiveGift: (params = {}) => vm.$u.post(`/Market/FullGive/selectFullGiveGift`, params),
		// 根据SKU ID获取商品列表
		getGoodsListBySkuIds: (params = {}) => vm.$u.post(`/Goods/ApiGoods/getGoodsBySkuIds`, params),
		/*------满额换购------*/
		// 获取可用的满额换购活动
		getAllUsableFullBuy: () => vm.$u.get(`/Market/ApiFullBuy/getUserAvailable`),
		/*------仓库------*/
		// 获取当前仓库列表
		getAllWarehouse: (params = {}) => vm.$u.post(`/Stock/Warehouse/getAllWarehouse`, params),
	};
}

export default {
	install
}
