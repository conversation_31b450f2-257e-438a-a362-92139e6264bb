<template>
  <ContainerTit>
    <div slot="headr">
      <el-button v-if="!fullGiveId" @click="delPauseSave(1)">清除暂存 </el-button>
      <el-button v-if="!fullGiveId" :loading="loading" @click="addPauseSave">暂存 </el-button>
      <el-button type="primary" :loading="loading" @click="subData">提交保存 </el-button>
    </div>
    <el-form ref="form" :rules="rules" :model="form" label-width="100px">
      <div class="detail-tab-item">
        <div class="detail-tab-title">活动信息</div>
        <div class="detail-tab-main">
          <el-form-item label="活动标题" prop="title">
            <el-input v-model="form.title" class="width200"></el-input>
          </el-form-item>
          <el-form-item label="活动时间" prop="time">
            <el-date-picker
              v-model="time"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="timeChange"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="使用范围" prop="shopId">
            <SelectShop v-model="form.shopId" :multiple="false" :enable="true" @getAllShop="getAllShop" />
            <el-button size="mini" type="text" @click="$router.push('/SystemSettings/liansuoguanli/AddShop')">
              【新建商铺】
            </el-button>
            <div class="form-tips">*每个满赠活动只能选择一个店铺</div>
          </el-form-item>
          <el-form-item label="适用仓库" prop="warehouseId">
            <SelectWarehouse
              v-model="form.warehouseId"
              :width="200"
              placeholder="请选择仓库（不选择则适用所有仓库）"
              :clearable="true"
              @getAllWarehouse="getAllWarehouse"
            />
            <div class="form-tips">*不选择则适用于所有仓库</div>
          </el-form-item>
          <el-form-item label="倍数赠送" prop="isMultipleGift">
            <el-radio-group v-model="form.isMultipleGift">
              <el-radio :label="5">启用</el-radio>
              <el-radio :label="4">关闭</el-radio>
            </el-radio-group>
            <div class="form-tips">*启用后，当购买数量是满赠条件的倍数时，赠品也按倍数发放（仅适用于数量型满赠）</div>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="5">启用</el-radio>
              <el-radio :label="4">关闭</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="互斥设置" prop="isMutex">
            <el-radio-group v-model="form.isMutex">
              <el-radio :label="5">互斥</el-radio>
              <el-radio :label="4">不互斥</el-radio>
            </el-radio-group>
            <div class="form-tips">*互斥：该活动不能与其他满赠活动同时使用</div>
          </el-form-item>
          <el-form-item label="叠加设置" prop="isCombinable">
            <el-radio-group v-model="form.isCombinable">
              <el-radio :label="5">可叠加</el-radio>
              <el-radio :label="4">不可叠加</el-radio>
            </el-radio-group>
            <div class="form-tips">*叠加：该活动可与多个非互斥满赠活动同时使用</div>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" class="width200"></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="detail-tab-item">
        <div class="detail-tab-title">客户限制</div>
        <div class="detail-tab-main">
          <el-form-item label="客户类型" prop="customerTypes">
            <el-checkbox-group v-model="form.customerTypes" class="checkbox-group">
              <el-checkbox v-for="item in customerTypeOptions" :key="item.value" :label="item.value">
                {{ item.label }}
              </el-checkbox>
            </el-checkbox-group>
            <div class="form-tips">*不选择则对所有客户类型生效</div>
          </el-form-item>
          <el-form-item label="客户标签" prop="customerTags">
            <el-checkbox-group v-model="form.customerTags" class="checkbox-group">
              <el-checkbox v-for="item in customerTagOptions" :key="item.id" :label="item.id">
                {{ item.name }}
              </el-checkbox>
            </el-checkbox-group>
            <div class="form-tips">*不选择则对所有客户标签生效</div>
          </el-form-item>

        </div>
      </div>

      <div class="detail-tab-item">
        <div class="detail-tab-title">活动规则</div>
        <div class="detail-tab-main">
          <el-form-item label="满赠阶梯" prop="amountRange">
            <div style="margin-bottom: 20px">
              <el-button type="primary" size="small" @click="addAmountLevel">添加阶梯</el-button>
              <div class="form-tips">*设置不同订单金额或商品数量可获得的不同赠品</div>
            </div>

            <div v-for="(level, levelIndex) in form.amountRange" :key="levelIndex" class="level-container">
              <div class="level-header">
                <div class="level-title">
                  <span>阶梯 {{ level.level }}: </span>
                  <span v-if="level.giftType === 1" style="margin-right: 10px">满{{ level.requiredAmount }}元</span>
                  <span v-else-if="level.giftType === 2" style="margin-right: 10px">满{{ level.requiredQuantity }}件</span>
                  <!-- 满赠类型选择 -->
                  <el-radio-group v-model="level.giftType" @change="handleGiftTypeChange(levelIndex)">
                    <el-radio :label="1">金额满赠</el-radio>
                    <el-radio :label="2">数量满赠</el-radio>
                  </el-radio-group>
                </div>
                <div class="level-actions">
                  <el-button size="mini" type="primary" @click="editAmountLevel(levelIndex)">编辑阶梯</el-button>
                  <el-button size="mini" type="danger" @click="removeAmountLevel(levelIndex)">删除阶梯</el-button>
                </div>
              </div>

              <div class="level-content">
                <!-- 金额满赠时显示计算方式 -->
                <div v-if="level.giftType === 1" class="calculation-method">
                  <span class="method-label">计算方式：</span>
                  <el-radio-group v-model="level.targetType" @change="handleTargetTypeChange(levelIndex)">
                    <el-radio :label="1">订单总金额</el-radio>
                    <el-radio :label="2">指定商品金额</el-radio>
                  </el-radio-group>
                </div>

                <!-- 数量满赠时显示指定商品选择 -->
                <div v-if="level.giftType === 2" class="target-products">
                  <div class="target-products-header">
                    <span>指定商品：</span>
                    <el-button size="small" type="primary" @click="selectTargetProductsForQuantity(levelIndex)">
                      选择商品
                    </el-button>
                    <span class="target-products-tips">*只统计这些商品的数量</span>
                  </div>
                  <el-table
                    v-if="level.targetSkuIds && level.targetSkuIds.length > 0"
                    :data="level.targetSkus || []"
                    border
                    style="width: 100%; margin-top: 10px"
                  >
                    <el-table-column label="商品编码" prop="code" width="120"></el-table-column>
                    <el-table-column label="商品名称" prop="title" width="200"></el-table-column>
                    <el-table-column label="规格" prop="spec" width="120">
                      <template slot-scope="scope">
                        {{
                          scope.row.unitName
                            ? `${scope.row.unitName}; ${scope.row.specGroup[0].specValueName || ""}`
                            : "加载中..."
                        }}
                      </template>
                    </el-table-column>
                    <el-table-column label="店铺" prop="shopName" width="120"></el-table-column>
                    <el-table-column label="满足数量" width="160">
                      <template slot-scope="scope">
                        <el-input-number
                          v-model="scope.row.requiredQuantity"
                          :min="1"
                          :max="99"
                          size="small"
                          @change="handleRequiredQuantityChange(levelIndex, scope.$index)"
                        ></el-input-number>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120">
                      <template slot-scope="scope">
                        <el-button type="text" @click="removeTargetProductForQuantity(levelIndex, scope.$index)"
                          >删除</el-button
                        >
                      </template>
                    </el-table-column>
                  </el-table>
                </div>

                <!-- 金额满赠指定商品 -->
                <div v-if="level.giftType === 1 && level.targetType === 2" class="target-products">
                  <div class="target-products-header">
                    <span>指定商品：</span>
                    <el-button size="small" type="primary" @click="selectTargetProducts(levelIndex)">
                      选择商品
                    </el-button>
                  </div>
                  <el-table
                    v-if="level.targetSkus && level.targetSkus.length > 0"
                    :data="level.targetSkus"
                    border
                    style="width: 100%; margin-top: 10px"
                  >
                    <el-table-column label="商品编码" prop="code" width="120"></el-table-column>
                    <el-table-column label="商品名称" prop="title" width="200"></el-table-column>
                    <el-table-column label="规格" prop="spec" width="120">
                      <template slot-scope="scope">
                        {{
                          scope.row.unitName
                            ? `${scope.row.unitName}; ${scope.row.specGroup[0].specValueName || ""}`
                            : "加载中..."
                        }}
                      </template>
                    </el-table-column>
                    <el-table-column label="店铺" prop="shopName" width="120"></el-table-column>
                    <el-table-column label="操作" width="120">
                      <template slot-scope="scope">
                        <el-button type="text" @click="removeTargetProduct(levelIndex, scope.$index)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>

                <!-- 赠品选择 -->
                <div style="margin-bottom: 10px">
                  <el-button size="small" type="primary" @click="() => handleSelectSingleGifts(levelIndex)">
                    选择赠品
                  </el-button>
                </div>

                <el-table :data="level.skus || []" border style="width: 100%">
                  <el-table-column label="商品编码" prop="code" width="135">
                    <template slot-scope="scope">
                      {{ scope.row.code || "加载中..." }}
                    </template>
                  </el-table-column>
                  <el-table-column label="商品名称" prop="title" width="200">
                    <template slot-scope="scope">
                      {{ scope.row.title || "加载中..." }}
                    </template>
                  </el-table-column>
                  <el-table-column label="规格" prop="spec" width="120">
                    <template slot-scope="scope">
                      {{
                        scope.row.unitName
                          ? `${scope.row.unitName}; ${scope.row.specGroup[0].specValueName || ""}`
                          : "加载中..."
                      }}
                    </template>
                  </el-table-column>
                  <el-table-column label="店铺" prop="shopName" width="120">
                    <template slot-scope="scope">
                      {{ scope.row.shopName || "加载中..." }}
                    </template>
                  </el-table-column>
                  <el-table-column label="赠送数量" width="160">
                    <template slot-scope="scope">
                      <el-input-number
                        v-model="scope.row.giftQuantity"
                        :min="1"
                        :max="99"
                        size="small"
                        @change="handleGiftQuantityChange(levelIndex, scope.$index)"
                      ></el-input-number>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template slot-scope="scope">
                      <el-button type="text" @click="handleDeleteSingleGift(levelIndex, scope.$index)"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>


              </div>
            </div>
          </el-form-item>
        </div>
      </div>
    </el-form>

    <!-- 设置金额阶梯对话框 -->
    <el-dialog
      :title="amountRangeDialogTitle"
      :visible.sync="amountRange_show"
      width="400px"
      @close="amountRange_show = false"
    >
      <el-form ref="rangeForm" :model="rangeForm" :rules="rangeRules" label-width="100px">
        <el-form-item v-if="isAddingLevel" label="阶梯等级" prop="level">
          <el-input-number v-model="rangeForm.level" :min="1" :max="100"></el-input-number>
        </el-form-item>
        <el-form-item label="满足金额" prop="requiredAmount">
          <el-input v-model="rangeForm.requiredAmount" type="number">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <el-button @click="amountRange_show = false">取消</el-button>
        <el-button type="primary" @click="confirmAmountRange">确定</el-button>
      </template>
    </el-dialog>

    <SaleGoodsSel
      v-if="goods_show"
      :is-show="goods_show"
      :shop-id="form.shopId"
      @cancel="goods_show = false"
      @confirm="selGoods"
    ></SaleGoodsSel>
  </ContainerTit>
</template>

<script>
import { addPauseSave, delPauseSave, getPauseSave } from "@/api/common";
import { getAllCustomerTagLib } from "@/api/Customer";
import { addFullGive, editFullGive, getFullGiveInfo } from "@/api/Market";
import { getCustomerSourceList } from "@/api/System";
import SaleGoodsSel from "@/component/goods/SaleGoodsSel.vue";
import SelectShop from "@/component/goods/SelectShop.vue";
import SelectWarehouse from "@/component/common/SelectWarehouse.vue";

export default {
  name: "AddFullGive",
  components: { SaleGoodsSel, SelectShop, SelectWarehouse },
  data() {
    return {
      loading: false,
      fullGiveId: null,
      title: "新增满赠",
      form: {
        title: "",
        startTime: "",
        endTime: "",
        shopId: "",
        warehouseId: null,
        isMultipleGift: 4,
        status: 5,
        amountRange: [],
        isMutex: 5,
        remark: "",
        isCombinable: 4,
        customerTypes: [],
        customerTags: [],
      },
      rules: {
        title: [{ required: true, message: "请输入活动标题", trigger: "blur" }],
        time: [
          {
            required: true,
            message: "请选择活动时间",
            trigger: "change",
            validator: (rule, value, callback) => {
              if (value && value.length === 2) {
                callback();
              } else if (this.form.startTime && this.form.endTime) {
                callback();
              } else {
                callback(new Error("请选择活动时间"));
              }
            },
          },
        ],
        shopId: [{ required: true, message: "请选择店铺", trigger: "change" }],
        amountRange: [{ required: true, message: "请添加满赠阶梯", trigger: "change" }],
      },
      customerTypeOptions: [
        { label: "普通客户", value: 1 },
        { label: "VIP客户", value: 2 },
        { label: "企业客户", value: 3 },
      ],
      customerTagOptions: [],
      time: null,
      shopList: [],

      // 记录商品选择器是哪种类型：1-赠品选择，2-金额满赠的指定商品，3-数量满赠的指定商品
      productSelectorType: 1,

      // 记录当前操作的阶梯索引
      currentLevelIndex: 0,
      amountRange_show: false,
      amountRangeDialogTitle: "添加金额阶梯",
      isAddingLevel: true,
      editingLevelIndex: -1,
      currentSelectingLevel: -1,
      currentSelectingCombination: -1,
      selectingGiftMode: "single",
      goods_show: false,
      rangeForm: {
        level: 1,
        requiredAmount: 0,
        requiredQuantity: 1,
      },
      rangeRules: {
        level: [{ required: true, message: "请输入阶梯等级" }],
        requiredAmount: [
          { required: true, message: "请输入满足金额" },
          { type: "number", min: 0.01, message: "金额必须大于0", trigger: "blur" },
        ],
        requiredQuantity: [
          { required: true, message: "请输入满足数量" },
          { type: "number", min: 1, message: "数量必须大于等于1", trigger: "blur" },
        ],
      },
      currentSelectingTargetLevel: -1,
    };
  },
  async created() {
    try {
      this.loading = true;

      // 并行加载客户类型和标签数据
      await Promise.all([this.getCustomerTypes(), this.getCustomerTags()]);

      // 如果是编辑模式，加载满赠活动数据
      if (this.$route.params.id) {
        this.fullGiveId = this.$route.params.id;
        await this.getInfo();
      } else {
        // 新建模式，尝试获取暂存数据
        await this.getPauseSave();
      }
    } catch (error) {
      console.error("初始化失败:", error);
      this.$message.error("初始化数据失败，请刷新页面重试");
    } finally {
      this.loading = false;
    }
  },
  methods: {
    // 加载满赠活动数据
    async getInfo() {
      const { data } = await getFullGiveInfo(this.fullGiveId);
      this.form = {
        ...data,
      };

      // 确保每个阶梯都有完整的数据结构
      if (this.form.amountRange && this.form.amountRange.length > 0) {
        this.form.amountRange.forEach((item, index) => {
          // 初始化基本字段
          if (!item.level) {
            item.level = index + 1;
          }
          if (!item.skus) {
            item.skus = [];
          }
          if (!item.giftSkuIds) {
            item.giftSkuIds = [];
          }
          if (!item.targetSkus) {
            item.targetSkus = [];
          }
          if (!item.targetSkuIds) {
            item.targetSkuIds = [];
          }
          // 处理数量满赠的指定商品数量信息
          if (item.giftType === 2 && item.targetSkuIds && item.targetSkuIds.length > 0) {
            const hasRequiredQuantity =
              typeof item.targetSkuIds[0] === "object" && "requiredQuantity" in item.targetSkuIds[0];

            if (hasRequiredQuantity && item.targetSkus && item.targetSkus.length > 0) {
              // 将requiredQuantity信息从targetSkuIds复制到targetSkus
              item.targetSkus.forEach((sku) => {
                const targetSkuInfo = item.targetSkuIds.find((info) => {
                  if (typeof info === "object") {
                    return info.skuId === sku.skuId;
                  }
                  return false;
                });

                if (targetSkuInfo && targetSkuInfo.requiredQuantity) {
                  sku.requiredQuantity = targetSkuInfo.requiredQuantity;
                } else {
                  sku.requiredQuantity = 1; // 默认值
                }
              });
            } else if (!hasRequiredQuantity && item.targetSkus && item.targetSkus.length > 0) {
              // 为targetSkus添加默认的requiredQuantity
              item.targetSkus.forEach((sku) => {
                sku.requiredQuantity = 1;
              });

              // 转换targetSkuIds为对象数组
              const newTargetSkuIds = [];
              for (let i = 0; i < item.targetSkuIds.length; i++) {
                const skuId = item.targetSkuIds[i];
                const targetSku = item.targetSkus.find((sku) => sku.skuId === skuId);

                newTargetSkuIds.push({
                  skuId: skuId,
                  requiredQuantity: targetSku && targetSku.requiredQuantity ? targetSku.requiredQuantity : 1,
                });
              }

              item.targetSkuIds = newTargetSkuIds;
            }
          }



          // 确保单品赠品有数量字段
          if (item.skus && item.skus.length > 0) {
            item.skus.forEach((sku) => {
              if (!sku.giftQuantity) {
                sku.giftQuantity = 1;
              }
            });
          }
        });

        // 按level排序
        this.form.amountRange.sort((a, b) => a.level - b.level);
      }

      this.form = this.$_common.deepClone(this.form);

      this.time = [data.startTime * 1000, data.endTime * 1000];

      // 获取客户类型和标签数据
      await Promise.all([this.getCustomerTypes(), this.getCustomerTags()]);

      // 在获取数据后，更新商品详情
      await this.updateSkuDetails();

      // 在下一个事件循环中验证表单，确保组件已经渲染完成
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.validateField("time");
        }
      });
    },
    async getPauseSave() {
      const result = await getPauseSave({
        key: "addFullGive" + this.onlineEnterpriseId,
      });
      if (result.errorcode === 0 && result.data?.form) {
        this.form = result.data.form;
        // 确保每个阶梯都有activeGiftTabName
        if (this.form.amountRange && this.form.amountRange.length > 0) {
          this.form.amountRange.forEach((item) => {
            if (!item.activeGiftTabName) {
              item.activeGiftTabName = "single";
            }
          });
        }

        // 处理时间数据
        if (this.form.startTime && this.form.endTime) {
          this.time = [this.form.startTime * 1000, this.form.endTime * 1000];
          // 在下一个事件循环中验证表单
          this.$nextTick(() => {
            if (this.$refs.form) {
              this.$refs.form.validateField("time");
            }
          });
        }
      }
    },
    async addPauseSave() {
      await addPauseSave(
        "addFullGive" + this.onlineEnterpriseId,
        {
          form: this.form,
          time: this.time,
        },
        "FullGive"
      );
      this.$message({
        message: "保存成功",
        type: "success",
      });
    },
    async delPauseSave(showMessage = 0) {
      await delPauseSave("addFullGive" + this.onlineEnterpriseId, "FullGive");
      if (showMessage) {
        this.$message({
          message: "清除暂存成功",
          type: "success",
        });
      }
    },
    async subData() {
      // 先检查时间是否已设置
      if (!this.time && (!this.form.startTime || !this.form.endTime)) {
        this.$message.error("请选择活动时间");
        return;
      }

      this.$refs.form.validate(async (valid) => {
        if (valid) {
          // 参数验证
          if (this.form.amountRange.length === 0) {
            this.$message.error("请添加至少一个满赠阶梯");
            return;
          }

          this.loading = true;

          // 验证每个阶梯的参数
          for (let level of this.form.amountRange) {
            // 验证满赠类型
            const giftType = level.giftType || 1;

            // 金额满赠类型检查
            if (giftType === 1) {
              if (!level.requiredAmount || level.requiredAmount <= 0) {
                this.$message.error(`阶梯${level.level}：金额必须大于0`);
                this.loading = false;
                return;
              }

              // 指定商品金额满赠时必须有指定商品
              if (level.targetType === 2 && (!level.targetSkuIds || level.targetSkuIds.length === 0)) {
                this.$message.error(`阶梯${level.level}：指定商品满额时必须选择商品`);
                this.loading = false;
                return;
              }
            }
            // 数量满赠类型检查
            else if (giftType === 2) {
              if (!level.requiredQuantity || level.requiredQuantity <= 0) {
                this.$message.error(`阶梯${level.level}：数量必须大于0`);
                this.loading = false;
                return;
              }

              // 数量满赠必须有指定商品
              if (!level.targetSkuIds || level.targetSkuIds.length === 0) {
                this.$message.error(`阶梯${level.level}：数量满赠必须选择指定商品`);
                this.loading = false;
                return;
              }
            }

            // 检查是否设置了赠品
            const hasGift = level.skus && level.skus.length > 0;

            if (!hasGift) {
              this.$message.error(`阶梯${level.level}：必须设置赠品`);
              this.loading = false;
              return;
            }
          }

          // 处理表单数据
          const formData = { ...this.form };

          // 处理阶梯数据，确保格式正确
          formData.amountRange = JSON.parse(JSON.stringify(this.form.amountRange));
          for (let level of formData.amountRange) {
            // 移除前端展示用的属性
            delete level.targetSkus;
            delete level.activeGiftTabName;

            // 数量满赠时确保有targetSkuIds
            if (level.giftType === 2) {
              level.targetSkuIds = level.targetSkuIds || [];
            }

            // 赠品数据处理
            if (level.skus && level.skus.length > 0) {
              level.giftSkuIds = level.skus.map((sku) => ({
                skuId: sku.skuId,
                quantity: sku.giftQuantity || 1,
              }));
            }

            // 删除前端临时数据
            delete level.skus;
          }

          try {
            let result;
            if (this.fullGiveId) {
              formData.id = this.fullGiveId;
              result = await editFullGive(this.fullGiveId, formData);
            } else {
              result = await addFullGive(formData);
            }

            this.$message.success("保存成功");
            this.$router.push("/Marketing/FullGive/FullGiveList");
          } catch (error) {
            console.error("保存失败", error);
            this.$message.error("保存失败：" + (error.message || "未知错误"));
          } finally {
            this.loading = false;
          }
        }
      });
    },
    // 添加新阶梯
    addAmountLevel() {
      // 打开添加阶梯对话框
      this.$prompt("请输入满额金额", "添加阶梯", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "number",
        inputValidator: (value) => {
          if (!value) return "金额不能为空";
          if (isNaN(value) || Number(value) <= 0) return "请输入大于0的数字";
          return true;
        },
      })
        .then(({ value }) => {
          // 添加新阶梯
          const newLevel = {
            level: this.form.amountRange.length + 1,
            giftType: 1, // 默认为金额满赠
            requiredAmount: Number(value), // 修复：使用正确的字段名
            requiredQuantity: 1, // 初始化数量为1，数量满赠时使用
            targetType: 1,
            skus: [], // 修复：初始化为空数组
            giftSkuIds: [], // 修复：初始化为空数组
            targetSkus: [], // 修复：初始化为空数组
            targetSkuIds: [],
            combinations: [],
            activeGiftTabName: "single",
          };
          this.form.amountRange.push(newLevel);
          this.$message.success("添加阶梯成功");
        })
        .catch(() => {});
    },
    // 编辑阶梯金额
    editAmountLevel(index) {
      const level = this.form.amountRange[index];
      const giftType = level.giftType || 1;

      if (giftType === 1) {
        // 金额满赠
        this.$prompt("请输入满额金额", "编辑金额", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          inputType: "number",
          inputValue: level.requiredAmount,
          inputValidator: (value) => {
            if (!value) return "金额不能为空";
            if (isNaN(value) || Number(value) <= 0) return "请输入大于0的数字";
            return true;
          },
        })
          .then(({ value }) => {
            level.requiredAmount = Number(value);
            this.$message.success("编辑阶梯金额成功");
          })
          .catch(() => {});
      } else if (giftType === 2) {
        // 数量满赠
        this.$prompt("请输入满足数量", "编辑数量", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          inputType: "number",
          inputValue: level.requiredQuantity,
          inputValidator: (value) => {
            if (!value) return "数量不能为空";
            if (isNaN(value) || Number(value) <= 0) return "请输入大于0的数字";
            if (!Number.isInteger(Number(value))) return "请输入整数";
            return true;
          },
        })
          .then(({ value }) => {
            level.requiredQuantity = Number(value);
            this.$message.success("编辑阶梯数量成功");
          })
          .catch(() => {});
      }
    },
    // 删除阶梯
    removeAmountLevel(index) {
      this.$confirm("确认删除该阶梯吗？删除后该阶梯的所有配置将丢失。", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.form.amountRange.splice(index, 1);
          // 重新排序阶梯等级
          this.form.amountRange.forEach((level, idx) => {
            level.level = idx + 1;
          });
          this.$message.success("删除阶梯成功");
        })
        .catch(() => {});
    },
    // 确认阶梯配置
    confirmAmountRange() {
      // 验证表单
      this.$refs.rangeForm.validate((valid) => {
        if (valid) {
          // 关闭对话框
          this.amountRange_show = false;
          this.$message.success("阶梯配置成功");
        } else {
          this.$message.error("请检查表单输入");
          return false;
        }
      });
    },
    // 处理满赠类型变更
    handleGiftTypeChange(levelIndex) {
      const level = this.form.amountRange[levelIndex];

      // 如果从金额满赠切换到数量满赠
      if (level.giftType === 2) {
        if (!level.requiredQuantity || level.requiredQuantity <= 0) {
          level.requiredQuantity = 1; // 默认设置为1件
        }
        // 清空金额满赠的计算方式
        level.targetType = 1;
        level.targetSkus = [];
        level.targetSkuIds = [];
      }
      // 如果从数量满赠切换到金额满赠
      else if (level.giftType === 1) {
        if (!level.requiredAmount || level.requiredAmount <= 0) {
          level.requiredAmount = 100; // 默认设置为100元
        }
      }
    },
    // 数量满赠选择指定商品
    selectTargetProductsForQuantity(levelIndex) {
      const level = this.form.amountRange[levelIndex];

      // 使用商品选择组件
      this.currentSelectingLevel = levelIndex;
      this.selectingGiftMode = "quantity";
      this.goods_show = true;
    },
    // 移除数量满赠的指定商品
    removeTargetProductForQuantity(levelIndex, productIndex) {
      const level = this.form.amountRange[levelIndex];

      if (level.targetSkus && level.targetSkus.length > 0) {
        // 从显示列表中移除
        const removedSku = level.targetSkus.splice(productIndex, 1)[0];

        // 从ID列表中移除
        if (level.targetSkuIds) {
          const idIndex = level.targetSkuIds.findIndex((item) => {
            if (typeof item === "object" && item.skuId === removedSku.skuId) {
              return true;
            } else if (item === removedSku.skuId) {
              return true;
            }
            return false;
          });
          if (idIndex > -1) {
            level.targetSkuIds.splice(idIndex, 1);
          }
        }
      }
    },
    // 处理满足数量变更
    handleRequiredQuantityChange(levelIndex, skuIndex) {
      const sku = this.form.amountRange[levelIndex].targetSkus[skuIndex];
      if (!sku.requiredQuantity || sku.requiredQuantity < 1) {
        sku.requiredQuantity = 1;
      }
      if (sku.requiredQuantity > 99) {
        sku.requiredQuantity = 99;
      }

      // 更新targetSkuIds中的数量信息
      const level = this.form.amountRange[levelIndex];
      const skuId = sku.skuId;

      // 检查targetSkuIds是否是对象数组还是简单ID数组
      let isObjectArray = false;
      if (level.targetSkuIds && level.targetSkuIds.length > 0) {
        isObjectArray = typeof level.targetSkuIds[0] === "object";
      }

      // 如果不是对象数组，需要转换为对象数组
      if (!isObjectArray) {
        level.targetSkuIds = level.targetSkuIds.map((id) => {
          if (id === skuId) {
            return { skuId: id, requiredQuantity: sku.requiredQuantity };
          }
          // 为其他ID也创建对象，默认数量为1
          const targetSku = level.targetSkus.find((item) => item.skuId === id);
          const quantity = targetSku && targetSku.requiredQuantity ? targetSku.requiredQuantity : 1;
          return { skuId: id, requiredQuantity: quantity };
        });
      } else {
        // 找到并更新现有对象
        const targetIndex = level.targetSkuIds.findIndex((item) => item.skuId === skuId);
        if (targetIndex > -1) {
          level.targetSkuIds[targetIndex].requiredQuantity = sku.requiredQuantity;
        }
      }
    },
    // 修改选择单品赠品的方法
    handleSelectSingleGifts(levelIndex) {
      if (!this.form.shopId) {
        this.$message.warning("请先选择使用范围（店铺）");
        return;
      }

      const level = this.form.amountRange[levelIndex];
      // 检查是否有组合赠品
      if (level.combinations && level.combinations.length > 0) {
        this.$message.warning("该阶梯已设置组合赠品，不能添加单品赠品");
        return;
      }

      this.currentSelectingLevel = levelIndex;
      this.selectingGiftMode = "single";
      this.goods_show = true;
    },

    // 删除赠品
    handleDeleteSingleGift(level, index) {
      this.$confirm("确认删除该赠品商品吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.form.amountRange[level].skus.splice(index, 1);
          this.$message.success("删除成功");
        })
        .catch(() => {});
    },

    // 修改计算方式变更处理方法
    handleTargetTypeChange(levelIndex) {
      const level = this.form.amountRange[levelIndex];
      if (level.targetType === 1) {
        // 切换到订单总金额时，清空指定商品
        this.$set(level, "targetSkus", []);
        this.$set(level, "targetSkuIds", []);
      }
    },
    // 修改选择指定商品方法
    selectTargetProducts(levelIndex) {
      if (!this.form.shopId) {
        this.$message.warning("请先选择使用范围（店铺）");
        return;
      }

      this.currentSelectingLevel = levelIndex;
      this.selectingGiftMode = "target";
      this.goods_show = true;
    },
    // 修改移除指定商品方法
    removeTargetProduct(levelIndex, productIndex) {
      const level = this.form.amountRange[levelIndex];
      if (!level.targetSkus) return;

      this.$confirm("确认删除该指定商品吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          level.targetSkus.splice(productIndex, 1);
          if (level.targetSkuIds) {
            level.targetSkuIds.splice(productIndex, 1);
          }
          this.$message.success("删除成功");
        })
        .catch(() => {});
    },
    // 修改商品选择处理方法
    async selGoods(selectedGoods) {
      this.goods_show = false;

      // 检查选择的商品是否属于已选择的店铺
      const invalidGoods = selectedGoods.filter((good) => good.shopId !== this.form.shopId);
      if (invalidGoods.length > 0) {
        this.$message.warning("部分商品不属于已选择的店铺，已自动过滤");
        selectedGoods = selectedGoods.filter((good) => good.shopId === this.form.shopId);
      }

      if (selectedGoods.length === 0) {
        return;
      }

      if (this.selectingGiftMode === "target") {
        // 处理指定商品选择
        const level = this.form.amountRange[this.currentSelectingLevel];
        if (!level.targetSkus) {
          this.$set(level, "targetSkus", []);
        }
        if (!level.targetSkuIds) {
          this.$set(level, "targetSkuIds", []);
        }

        // 检查是否已存在相同的商品
        const existingSkuIds = level.targetSkuIds.map((item) => (typeof item === "object" ? item.skuId : item));
        const newGoods = selectedGoods.filter((good) => !existingSkuIds.includes(good.skuId));

        if (newGoods.length === 0) {
          this.$message.warning("所选商品已存在于当前阶梯中");
          return;
        }

        // 添加新商品到指定商品列表
        level.targetSkus = level.targetSkus.concat(newGoods);
        level.targetSkuIds = level.targetSkuIds.concat(newGoods.map((good) => good.skuId));
      } else if (this.selectingGiftMode === "quantity") {
        // 处理数量满赠的指定商品选择
        const level = this.form.amountRange[this.currentSelectingLevel];
        if (!level.targetSkus) {
          this.$set(level, "targetSkus", []);
        }
        if (!level.targetSkuIds) {
          this.$set(level, "targetSkuIds", []);
        }

        // 检查targetSkuIds是否是对象数组
        const isObjectArray = level.targetSkuIds.length > 0 && typeof level.targetSkuIds[0] === "object";

        // 获取现有的SKU ID列表
        const existingSkuIds = level.targetSkuIds.map((item) => {
          return typeof item === "object" ? item.skuId : item;
        });

        const newGoods = selectedGoods.filter((good) => !existingSkuIds.includes(good.skuId));

        if (newGoods.length === 0) {
          this.$message.warning("所选商品已存在于当前阶梯中");
          return;
        }

        // 为新商品添加requiredQuantity属性
        const enhancedNewGoods = newGoods.map((good) => {
          return {
            ...good,
            requiredQuantity: 1, // 默认满足数量为1
          };
        });

        // 添加新商品到指定商品列表
        level.targetSkus = level.targetSkus.concat(enhancedNewGoods);

        // 根据现有格式添加到targetSkuIds
        if (isObjectArray) {
          // 如果是对象数组，添加对象
          level.targetSkuIds = level.targetSkuIds.concat(
            enhancedNewGoods.map((good) => ({ skuId: good.skuId, requiredQuantity: good.requiredQuantity }))
          );
        } else {
          // 如果是简单ID数组，先转换为对象数组
          if (level.targetSkuIds.length > 0) {
            // 将现有ID转换为对象
            level.targetSkuIds = level.targetSkuIds.map((id) => {
              const existingSku = level.targetSkus.find((sku) => sku.skuId === id);
              return {
                skuId: id,
                requiredQuantity: existingSku && existingSku.requiredQuantity ? existingSku.requiredQuantity : 1,
              };
            });
          }

          // 添加新的对象
          level.targetSkuIds = level.targetSkuIds.concat(
            enhancedNewGoods.map((good) => ({ skuId: good.skuId, requiredQuantity: good.requiredQuantity }))
          );
        }
      } else if (this.selectingGiftMode === "single") {
        // 单品赠品
        if (!this.form.amountRange[this.currentSelectingLevel].skus) {
          this.$set(this.form.amountRange[this.currentSelectingLevel], "skus", []);
        }

        // 检查是否已存在相同的商品
        const existingSkuIds = this.form.amountRange[this.currentSelectingLevel].skus.map((item) => item.skuId);
        const newGoods = selectedGoods.filter((good) => !existingSkuIds.includes(good.skuId));

        if (newGoods.length === 0) {
          this.$message.warning("所选商品已存在于当前阶梯中");
          return;
        }

        // 添加新商品到当前阶梯的单品列表
        this.form.amountRange[this.currentSelectingLevel].skus = this.form.amountRange[
          this.currentSelectingLevel
        ].skus.concat(
          newGoods.map((good) => ({
            ...good,
            giftQuantity: 1, // 初始化赠送数量为1
          }))
        );
      }
    },
    // 处理单品赠品数量变更
    handleGiftQuantityChange(levelIndex, skuIndex) {
      const sku = this.form.amountRange[levelIndex].skus[skuIndex];
      if (!sku.giftQuantity || sku.giftQuantity < 1) {
        sku.giftQuantity = 1;
      }
      if (sku.giftQuantity > 99) {
        sku.giftQuantity = 99;
      }
    },
    async getCustomerTypes() {
      try {
        const { data } = await getCustomerSourceList();
        this.customerTypeOptions = data.map((item) => ({
          value: item.id,
          label: item.name,
        }));
      } catch (error) {
        console.error("获取客户类型失败:", error);
        this.$message.error("获取客户类型失败");
      }
    },
    async getCustomerTags() {
      try {
        const { data } = await getAllCustomerTagLib();
        this.customerTagOptions = data.map((item) => ({
          id: item.id,
          name: item.name,
        }));
      } catch (error) {
        console.error("获取客户标签失败:", error);
        this.$message.error("获取客户标签失败");
      }
    },
    getAllWarehouse(warehouseList) {
      // SelectWarehouse 组件的回调，可以在这里处理仓库列表数据
      // 目前不需要特殊处理，组件内部已经处理了数据
    },
    // 修改提交数据处理方法
    getSubmitData() {
      const submitData = {
        ...this.form,
        startTime: this.time[0],
        endTime: this.time[1],
      };

      // 处理每个阶梯中的数据
      submitData.amountRange = submitData.amountRange.map((level) => {
        const { activeGiftTabName, ...rest } = level;

        // 处理指定商品数据
        if (rest.targetType === 2 && rest.targetSkus && rest.targetSkus.length > 0) {
          rest.targetSkuIds = rest.targetSkus.map((sku) => sku.skuId);
        } else if (rest.targetType === 2) {
          rest.targetSkuIds = [];
        }

        // 处理赠品数据
        if (rest.skus && rest.skus.length > 0) {
          rest.giftSkuIds = rest.skus.map((sku) => ({
            skuId: sku.skuId,
            quantity: sku.giftQuantity || 1,
          }));
        } else {
          rest.giftSkuIds = [];
        }

        // 删除临时数据
        delete rest.skus;
        delete rest.targetSkus;

        return rest;
      });

      return submitData;
    },
    // 修改验证方法
    validateGiftData() {
      let isValid = true;
      let errorMessage = "";

      // 检查是否有阶梯
      if (!this.form.amountRange || this.form.amountRange.length === 0) {
        return {
          isValid: false,
          message: "请至少添加一个满赠阶梯",
        };
      }

      // 检查每个阶梯
      for (let i = 0; i < this.form.amountRange.length; i++) {
        const level = this.form.amountRange[i];

        // 检查阶梯金额
        if (!level.requiredAmount || level.requiredAmount <= 0) {
          return {
            isValid: false,
            message: `第${i + 1}个阶梯的满足金额必须大于0`,
          };
        }

        // 检查阶梯顺序
        if (i > 0 && level.requiredAmount <= this.form.amountRange[i - 1].requiredAmount) {
          return {
            isValid: false,
            message: `第${i + 1}个阶梯的满足金额必须大于第${i}个阶梯`,
          };
        }

        // 检查赠品互斥规则
        const hasSingleGifts = level.skus && level.skus.length > 0;
        const hasCombinations = level.combinations && level.combinations.length > 0;

        if (hasSingleGifts && hasCombinations) {
          return {
            isValid: false,
            message: `第${i + 1}个阶梯不能同时设置单品赠品和组合赠品`,
          };
        }

        if (!hasSingleGifts && !hasCombinations) {
          return {
            isValid: false,
            message: `第${i + 1}个阶梯必须设置单品赠品或组合赠品`,
          };
        }

        // 检查组合赠品
        if (hasCombinations) {
          for (let j = 0; j < level.combinations.length; j++) {
            const combination = level.combinations[j];
            if (!combination.skus || combination.skus.length === 0) {
              return {
                isValid: false,
                message: `第${i + 1}个阶梯的第${j + 1}个组合中必须添加商品`,
              };
            }
          }
        }

        // 检查单品赠品数量
        if (hasSingleGifts) {
          for (let j = 0; j < level.skus.length; j++) {
            const sku = level.skus[j];
            if (!sku.giftQuantity || sku.giftQuantity < 1) {
              return {
                isValid: false,
                message: `第${i + 1}个阶梯的第${j + 1}个单品赠品未设置数量或数量小于1`,
              };
            }
            if (sku.giftQuantity > 99) {
              return {
                isValid: false,
                message: `第${i + 1}个阶梯的第${j + 1}个单品赠品数量不能超过99`,
              };
            }
          }
        }
      }

      return {
        isValid: true,
        message: "",
      };
    },
    timeChange(val) {
      if (val && val.length === 2) {
        this.form.startTime = val[0] / 1000;
        this.form.endTime = this.$NP.plus(val[1] / 1000, 86399);
        this.time = val;
        // 手动验证时间字段
        this.$refs.form.validateField("time");
      } else {
        this.form.startTime = "";
        this.form.endTime = "";
        this.time = null;
      }
    },
    getAllShop(list) {
      if (!this.fullGiveId) {
        this.form.shopId = list[0]?.id || null;
      }
    },
    // 修改 updateSkuDetails 方法
    async updateSkuDetails() {
      // 由于后端getInfo接口已经返回完整的SKU信息
      // 包括规格名称、单位等，这里不需要额外请求
      return;
    },
  },
};
</script>

<style lang="scss" scoped>
.level-container {
  margin-bottom: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  overflow: hidden;

  .level-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;

    .level-title {
      font-weight: bold;
      font-size: 16px;
    }

    .level-actions {
      display: flex;
      gap: 10px;
    }
  }

  .level-content {
    padding: 15px;

    .el-tabs {
      background-color: #fff;
    }
  }
}



.form-tips {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
  margin-top: 5px;
}

.width200 {
  width: 200px;
}

:deep(.el-tabs__content) {
  overflow: visible;
}

:deep(.el-table) {
  td {
    padding: 8px 0;
  }
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  :deep(.el-checkbox) {
    margin-right: 0;
    margin-left: 0;
  }
}

.calculation-method {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;

  .method-label {
    margin-right: 10px;
    font-weight: bold;
  }
}

.target-products {
  margin-bottom: 15px;

  .target-products-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    span {
      margin-right: 10px;
      font-weight: bold;
    }
  }
}
</style>
