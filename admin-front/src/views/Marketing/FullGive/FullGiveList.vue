<template>
  <ContainerQuery>
    <div slot="left">
      <el-button
        v-if="$accessCheck($Access.FullGive_addFullGive)"
        type="primary"
        size="small"
        @click="$router.push('/Marketing/FullGive/AddFullGive')"
      >
        新增满赠
      </el-button>
    </div>
    <div slot="more">
      <el-form :inline="true" size="small" style="margin-bottom: 0">
        <el-form-item>
          <el-input v-model="search_form.title" placeholder="满赠标题" clearable style="width: 220px">
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="fullGive_list">
      <el-table-column label="满赠标题" prop="title" fixed show-overflow-tooltip min-width="160"></el-table-column>
      <el-table-column label="活动商铺" prop="shopName"></el-table-column>
      <el-table-column label="适用仓库" prop="warehouseName" width="120">
        <template slot-scope="scope">
          {{ scope.row.warehouseName || '全部仓库' }}
        </template>
      </el-table-column>
      <el-table-column label="倍数赠送" prop="isMultipleGift" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.isMultipleGift === 5" class="success-status">启用</span>
          <span v-else class="danger-status">关闭</span>
        </template>
      </el-table-column>
      <el-table-column label="满赠类型" prop="giftType" width="120">
        <template slot-scope="scope">
          <el-tag v-if="hasAmountType(scope.row, 1)" type="primary">金额满赠</el-tag>
          <el-tag v-if="hasAmountType(scope.row, 2)" type="success">数量满赠</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="活动内容" prop="content" min-width="80">
        <template slot-scope="scope">
          <span class="click-div" @click="showContent(scope.row)">查看</span>
        </template>
      </el-table-column>
      <el-table-column label="活动时间" prop="activityTime" width="260">
        <template slot-scope="scope">
          {{
            $_common.formatDate(scope.row.startTime, "yyyy/MM/dd") +
            "～" +
            $_common.formatDate(scope.row.endTime, "yyyy/MM/dd")
          }}
        </template>
      </el-table-column>
      <el-table-column label="叠加设置" prop="isCombinable" width="100">
        <template slot-scope="scope">
          {{ scope.row.isCombinable === 5 ? "可叠加" : "不可叠加" }}
        </template>
      </el-table-column>

      <el-table-column label="状态" prop="status">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            class="switchStyle"
            active-color="#36B365"
            :active-value="5"
            :inactive-value="4"
            active-text="启用"
            inactive-text="禁用"
            :disabled="!$accessCheck($Access.FullGive_updateStatus)"
            @change="activityStatus($event, scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime, "yyyy/MM/dd") }}
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" width="200" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            :disabled="!$accessCheck($Access.FullGive_editFullGive)"
            type="text"
            size="small"
            @click="$router.push(`/Marketing/FullGive/EditFullGive/${scope.row.id}`)"
          >
            编辑
          </el-button>
          <el-button
            :disabled="!$accessCheck($Access.FullGive_delFullGive)"
            type="text"
            size="small"
            @click="delData(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>

    <el-dialog :visible="content_show" width="800px" title="活动内容" @close="content_show = false">
      <div style="display: flex">
        <div style="width: 200px">
          <el-tabs v-model="tabName" tab-position="left" @tab-click="handleTabClick">
            <el-tab-pane v-for="(item, index) in amountRange" :key="index" :name="item.name">
              <template slot="label">
                <span v-if="item.giftType === 1">满{{ item.price }}元</span>
                <span v-else-if="item.giftType === 2">满{{ item.quantity }}件</span>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div v-if="amountRangeItem" style="flex: 1; margin-top: 20px">
          <!-- 显示满赠类型信息 -->
          <div class="gift-type-info" style="margin-bottom: 15px">
            <el-tag v-if="amountRangeItem.giftType === 1" type="primary">金额满赠</el-tag>
            <el-tag v-else-if="amountRangeItem.giftType === 2" type="success">数量满赠</el-tag>

            <template v-if="amountRangeItem.giftType === 1">
              <span v-if="amountRangeItem.targetType === 1" style="margin-left: 10px"
                >订单总金额满{{ amountRangeItem.price }}元</span
              >
              <span v-else-if="amountRangeItem.targetType === 2" style="margin-left: 10px"
                >指定商品金额满{{ amountRangeItem.price }}元</span
              >
            </template>
            <template v-else-if="amountRangeItem.giftType === 2">
              <span style="margin-left: 10px">指定商品数量满{{ amountRangeItem.quantity }}件</span>
            </template>
          </div>

          <!-- 显示指定商品（如果有） -->
          <div
            v-if="
              (amountRangeItem.giftType === 1 && amountRangeItem.targetType === 2) || amountRangeItem.giftType === 2
            "
            style="margin-bottom: 15px"
          >
            <div style="font-weight: bold; margin-bottom: 10px">指定商品：</div>
            <el-table :data="amountRangeItem.targetSkus || []" style="width: 100%">
              <el-table-column label="商品编码" prop="code" width="150"></el-table-column>
              <el-table-column label="商品名称" prop="title" width="200"></el-table-column>
              <el-table-column label="店铺" prop="shopName" width="150"></el-table-column>
            </el-table>
          </div>

          <!-- 显示赠品信息 -->
          <div style="font-weight: bold; margin-bottom: 10px">赠品：</div>
          <el-table :data="amountRangeItem.skus || []">
            <el-table-column label="商品编码" prop="code" width="150"></el-table-column>
            <el-table-column label="商品名称" prop="title" width="200"></el-table-column>
            <el-table-column label="赠送数量" prop="giftQuantity" width="100"></el-table-column>
            <el-table-column label="店铺" prop="shopName" width="150"></el-table-column>
          </el-table>

          <!-- 显示组合赠品信息（如果有） -->
          <div v-if="amountRangeItem.combinations && amountRangeItem.combinations.length > 0" style="margin-top: 20px">
            <div style="font-weight: bold; margin-bottom: 10px">组合赠品：</div>
            <div
              v-for="(combo, comboIndex) in amountRangeItem.combinations"
              :key="comboIndex"
              style="margin-bottom: 15px"
            >
              <div style="background: #f5f7fa; padding: 8px; margin-bottom: 10px">
                {{ combo.name || "组合" + (comboIndex + 1) }} (限制数量: {{ combo.limit || 1 }})
              </div>
              <el-table :data="combo.skus || []">
                <el-table-column label="商品编码" prop="code" width="150"></el-table-column>
                <el-table-column label="商品名称" prop="title" width="200"></el-table-column>
                <el-table-column label="店铺" prop="shopName" width="150"></el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
      <template slot="footer">
        <el-button @click="content_show = false">关闭</el-button>
      </template>
    </el-dialog>
  </ContainerQuery>
</template>

<script>
import { delFullGive, getAllFullGive, getFullGiveInfo, updateStatusFullGive } from "@/api/Market";

export default {
  name: "FullGiveList",
  data() {
    return {
      fullGive_list: [],
      page: 1,
      pageSize: 10,
      total: 0,
      search_form: {
        title: "",
        status: "",
      },
      amountRange: [],
      amountRangeItem: null,
      amountRangeIndex: null,
      content_show: false,
      tabName: null,
    };
  },
  created() {
    this.getData();
  },
  methods: {
    async getData() {
      const params = {
        ...this.search_form,
        page: this.page,
        pageSize: this.pageSize,
      };
      const { data } = await getAllFullGive(params);
      this.fullGive_list = data;
    },
    pageChange(page) {
      this.page = page;
      this.getData();
    },
    sizeChange(size) {
      this.pageSize = size;
      this.pageChange(1);
    },
    async showContent(row) {
      const { data } = await getFullGiveInfo(row.id);
      this.amountRange = data.amountRange;
      if (this.amountRange.length > 0) {
        this.tabName = this.amountRange[0].name;
        this.amountRangeItem = this.amountRange[0];
        this.amountRangeIndex = 0;
      }
      this.content_show = true;
    },
    async delData(id) {
      await delFullGive(id);
      this.$message({
        type: "success",
        message: "删除成功！",
      });
      this.pageChange(1);
    },
    handleTabClick(tabObj) {
      this.amountRangeIndex = parseInt(tabObj.index);
      this.amountRangeItem = this.amountRange[this.amountRangeIndex];
    },
    async activityStatus(val, row) {
      try {
        const data = await updateStatusFullGive(row.id, {
          status: val,
        });
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      } catch (e) {
        this.getData();
      }
    },
    hasAmountType(row, type) {
      return row.amountRange.some((item) => item.giftType === type);
    },
  },
};
</script>

<style scoped></style>
