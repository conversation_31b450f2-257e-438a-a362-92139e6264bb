<!--仓库选择组件-->
<template>
  <div class="dis-inline">
    <el-select
      :value="value"
      :placeholder="placeholder"
      :clearable="clearable"
      filterable
      remote
      :size="size"
      :multiple="multiple"
      :disabled="disabled"
      :remote-method="remoteMethod"
      :loading="loading"
      :style="{ width: width + 'px' }"
      @change="selChange"
      @clear="clearChange"
    >
      <el-option v-for="(item, index) in warehouse_list" :key="index" :label="item.warehouseName" :value="item.id"></el-option>
    </el-select>
    <span class="el-icon-arrow-down sel-icon" style="margin-right: 15px"></span>
  </div>
</template>

<script>
import { getAllWarehouse } from "@/api/Stock";
export default {
  name: "SelectWarehouse",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: [Number, String, Array],
      default: "",
    },
    width: {
      type: [Number, String],
      default: 200,
    },
    placeholder: {
      type: String,
      default: "请选择仓库",
    },
    size: {
      type: String,
      default: "",
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    // 是否只显示启用的仓库
    enableOnly: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      warehouse_list: [],
      loading: false,
      page: 1,
      pre_page: 999,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    remoteMethod(query) {
      if (query !== "") {
        this.loading = true;
        this.searchList(query);
      } else {
        this.getList();
      }
    },
    clearChange() {
      this.$emit("clear");
    },
    selChange(val) {
      if (!val) return;
      let row = [];
      if (!this.multiple) {
        row = this.warehouse_list.filter((item) => item.id === val);
      } else {
        row = this.warehouse_list.filter((item) => val.indexOf(item.id) > -1);
      }
      this.$emit("change", val, row);
    },
    async getList() {
      const params = {
        page: this.page,
        pageSize: this.pre_page,
      };
      
      // 如果只显示启用的仓库
      if (this.enableOnly) {
        params.enableStatus = 5;
      }
      
      const data = await getAllWarehouse(params);
      this.loading = false;

      this.warehouse_list = data.data;
      this.$emit("getAllWarehouse", data.data);
      if (this.isDefault && data.data.length > 0) {
        this.$emit("default", data.data[0].id, data.data[0]);
      }
    },
    async searchList(keyword) {
      const params = {
        page: this.page,
        pageSize: this.pre_page,
        keyword: keyword,
      };
      
      // 如果只显示启用的仓库
      if (this.enableOnly) {
        params.enableStatus = 5;
      }
      
      const data = await getAllWarehouse(params);
      this.loading = false;

      this.warehouse_list = data.data;
    },
  },
};
</script>

<style scoped>
.dis-inline {
  display: inline-block;
  position: relative;
}

.sel-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: #c0c4cc;
}
</style>
