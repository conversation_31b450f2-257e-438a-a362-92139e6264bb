# 功能-文档映射关系分析报告

## 分析概述

本报告基于 `menus.js` 中的应用功能菜单结构，对比现有帮助文档的覆盖情况，建立功能与文档的映射关系表。

**分析时间**：2025年1月21日  
**数据源**：`menus.js` 功能菜单 + `help-docs/src/content/docs/` 文档结构  
**目标**：识别文档缺失的功能点，确保文档覆盖所有主要功能

## 映射关系表

### 1. 概况模块
| 功能路径 | 功能名称 | 对应文档 | 文档状态 | 优先级 |
|---------|---------|---------|---------|--------|
| `/index` | 经营概况 | `overview/` | 🔴 缺失 | 高 |

**缺失文档**：
- 经营概况页面使用指南
- 数据统计图表说明
- 关键指标解释

### 2. 商品管理模块
| 功能路径 | 功能名称 | 对应文档 | 文档状态 | 优先级 |
|---------|---------|---------|---------|--------|
| `/goods/sale/PublishGoods` | 商品列表 | `goods/manage/publish-goods.md` | 🟡 部分 | 中 |
| `/goods/manageG/BaseDataList` | 商品资料 | `goods/manage/base-data-list.md` | 🟢 完整 | 低 |
| `/goods/manageG/GoodsClassify` | 商品分类 | `goods/manage/goods-classify.md` | 🟡 部分 | 中 |
| `/goods/manageG/BrandManage` | 商品品牌 | `goods/manage/brand-manage.md` | 🟡 部分 | 中 |
| `/goods/manageG/UnitMeasurement` | 单位管理 | `goods/manage/unit-measurement.md` | 🟡 部分 | 中 |
| `/goods/manageG/SpecManage` | 属性管理 | `goods/manage/spec-manage.md` | 🟡 部分 | 中 |
| `/goods/goodsemprego/GoodsEmprego` | 商品服务 | `goods/manage/goods-emprego.md` | 🟡 部分 | 中 |
| `/goods/manageG/GoodsGrouping` | 商品分组 | `goods/manage/goods-grouping.md` | 🟡 部分 | 中 |
| `/goods/sale/PriceTable` | 价格表 | `goods/price/price-table.md` | 🟡 部分 | 中 |
| `/goods/sale/AdjustPriceGoods` | 调价单 | `goods/price/adjust-price-goods.md` | 🟡 部分 | 中 |

**文档覆盖率**：70%（7/10个功能有对应文档）  
**完整度**：40%（仅1个文档完整）

### 3. 订单管理模块
| 功能路径 | 功能名称 | 对应文档 | 文档状态 | 优先级 |
|---------|---------|---------|---------|--------|
| `/order/manageO/orderQuery` | 订货单 | `order/manage/order-query.md` | 🟢 完整 | 低 |
| `/order/manageO/selfOrder` | 自提单 | `order/manage/self-pickup.md` | 🟡 部分 | 中 |
| `/order/manageO/ReturnWarehousingOrder` | 退货单 | `order/manage/return-order.md` | 🟡 部分 | 中 |
| `/order/manageO/OrderAdd` | 代客下单 | `order/manage/proxy-order.md` | 🟡 部分 | 中 |
| `/order/SaleTotalForm/GoodsForm` | 商品汇总表 | `order/statement/` | 🔴 缺失 | 高 |
| `/order/SaleTotalForm/CustomerForm` | 客户汇总表 | `order/statement/` | 🔴 缺失 | 高 |
| `/order/SaleTotalForm/StaffForm` | 人员汇总表 | `order/statement/` | 🔴 缺失 | 高 |

**文档覆盖率**：57%（4/7个功能有对应文档）  
**完整度**：14%（仅1个文档完整）

### 4. 客户管理模块
| 功能路径 | 功能名称 | 对应文档 | 文档状态 | 优先级 |
|---------|---------|---------|---------|--------|
| `/Customer/CustomerAdmin/CustomerList` | 客户列表 | `customer/manage/customer-list.md` | 🟢 完整 | 低 |
| `/Customer/CustomerAdmin/CustomerType` | 客户类型 | `customer/manage/customer-type.md` | 🟡 部分 | 中 |
| `/Customer/CustomerAdmin/LabelManagement` | 标签管理 | `customer/manage/label-management.md` | 🟡 部分 | 中 |
| `/Customer/CustomerCheck/NotCheck` | 未审核 | `customer/check/unaudited.md` | 🟡 部分 | 中 |
| `/Customer/CustomerCheck/noPerfectData` | 待完善资料 | `customer/check/incomplete-data.md` | 🟡 部分 | 中 |
| `/Customer/CustomerBehavior/BrowsingHistory` | 浏览记录 | `customer/behavior/browsing-history.md` | 🟡 部分 | 中 |
| `/Customer/CustomerBehavior/PurchaseHistory` | 购买记录 | `customer/behavior/purchase-history.md` | 🟡 部分 | 中 |
| `/Customer/CustomerBehavior/DemandReporting` | 需求提报 | `customer/behavior/demand-report.md` | 🟡 部分 | 中 |
| `/Customer/CustomerStatements/AnomalyAnalysisTable` | 异常客户分析表 | `customer/statement/abnormal-analysis.md` | 🟡 部分 | 中 |
| `/Customer/CustomerStatements/PullNewStatistics` | 拉新统计 | `customer/statement/new-customer-stats.md` | 🟡 部分 | 中 |
| `/Customer/CustomerStatements/VisitRepor` | 拜访报表 | `customer/statement/visit-report.md` | 🟡 部分 | 中 |
| `/Customer/CustomerStatements/CustomerMap` | 客户分布图 | `customer/statement/customer-map.md` | 🟡 部分 | 中 |

**文档覆盖率**：100%（12/12个功能有对应文档）  
**完整度**：8%（仅1个文档完整）

### 5. 采购管理模块
| 功能路径 | 功能名称 | 对应文档 | 文档状态 | 优先级 |
|---------|---------|---------|---------|--------|
| `/Purchase/ManageP/PurchaseOrder` | 采购单 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Purchase/ManageP/PurchaseReturnOrder` | 采购退货单 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Purchase/ManageP/Supplier` | 供应商 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Purchase/ManageP/Stockout` | 缺货单 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Purchase/ManageP/Merchant` | 商户采购单 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Purchase/CostPrice/CostPriceList` | 成本价 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Purchase/ManageP/PurchaseDetail` | 采购明细表 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Purchase/PurchaseTotalForm/GoodsForm` | 商品汇总表 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Purchase/PurchaseTotalForm/SupplierForm` | 供应商汇总表 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Purchase/PurchaseTotalForm/StaffForm` | 采购员汇总表 | 🔴 缺失 | 🔴 缺失 | 高 |

**文档覆盖率**：0%（0/10个功能有对应文档）  
**完整度**：0%

### 6. 库存管理模块
| 功能路径 | 功能名称 | 对应文档 | 文档状态 | 优先级 |
|---------|---------|---------|---------|--------|
| `/stock/OutIn/outgoing` | 出库管理 | `stock/section1/out.md` | 🟡 部分 | 中 |
| `/stock/OutIn/storage` | 入库管理 | `stock/section1/in.md` | 🟡 部分 | 中 |
| `/stock/WarehouseAdmin/ShelfLife` | 保质期查询 | `stock/section2/expiry.md` | 🟡 部分 | 中 |
| `/stock/WarehouseManagement/inventoryInfo` | 盘点单 | `stock/section3/stocktake.md` | 🟡 部分 | 中 |
| `/stock/WarehouseManagement/AllocationForm` | 调拨单 | `stock/section3/allocate.md` | 🟡 部分 | 中 |
| `/stock/WarehouseManagement/LossReport` | 报损单 | `stock/section3/loss-report.md` | 🟡 部分 | 中 |
| `/stock/WarehouseManagement/Management` | 仓库管理 | `stock/section3/warehouse.md` | 🟡 部分 | 中 |
| `/stock/WarehouseManagement/WarehouseArea` | 库区管理 | `stock/section3/warehouse-area.md` | 🟡 部分 | 中 |
| `/stock/WarehouseManagement/LocationManagement` | 库位管理 | `stock/section3/warehouse-location.md` | 🟡 部分 | 中 |
| `/stock/WarehouseAdmin/query` | 库存查询 | `stock/section2/query.md` | 🟡 部分 | 中 |
| `/stock/WarehouseAdmin/warehouseInventory` | 仓库库存 | `stock/section2/warehouse-inventory.md` | 🟡 部分 | 中 |
| `/stock/summary/WarehouseStockSummary` | 库存汇总 | `stock/section2/inventory-summary.md` | 🟡 部分 | 中 |
| `/stock/WarehouseAdmin/flowing` | 库存流水 | `stock/section2/flow.md` | 🟡 部分 | 中 |
| `/stock/WarehouseAdmin/Batch` | 批次流水 | `stock/section2/batch.md` | 🟡 部分 | 中 |
| `/stock/supplier/SupplierInventory` | 供应商库存 | `stock/supplier/supplier-inventory.md` | 🟡 部分 | 中 |
| `/stock/supplier/SupplierInventoryFlowing` | 库存流水 | `stock/supplier/supplier-inventory-flowing.md` | 🟡 部分 | 中 |

**文档覆盖率**：100%（16/16个功能有对应文档）  
**完整度**：0%（无完整文档）

### 7. 财务管理模块
| 功能路径 | 功能名称 | 对应文档 | 文档状态 | 优先级 |
|---------|---------|---------|---------|--------|
| `/Finance/Receivable/CustomerBalance` | 客户往来汇总表 | `finance/section1/customer-balance.md` | 🟡 部分 | 中 |
| `/Finance/Receivable/ReceivableList` | 应收单列表 | `finance/section1/receivables.md` | 🟢 完整 | 低 |
| `/Finance/Receivable/ReceiptList` | 收款单列表 | `finance/section1/receipts.md` | 🟡 部分 | 中 |
| `/Finance/Receivable/ApplyReceipt` | 收款申请单 | `finance/section1/apply-receipt.md` | 🟡 部分 | 中 |
| `/Finance/Receivable/CustomerBalanceDetail` | 客户往来明细表 | `finance/section1/customer-balance-detail.md` | 🟡 部分 | 中 |
| `/Finance/Cashier/SellRefundForm/1` | 销售退款单 | `finance/section1/sales-refund.md` | 🟡 部分 | 中 |
| `/Finance/Handle/SupplierBalance` | 供应商往来汇总表 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Finance/Handle/HandleList` | 应付单列表 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Finance/Handle/PaymentList` | 付款单列表 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Finance/Handle/SupplierBalanceDetails` | 供应商往来明细表 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Finance/Cashier/FunTransfer` | 资金转账单 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Finance/Cashier/AccountList` | 资金账户管理 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Finance/Cashier/AccountDetails` | 账户明细查询 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Finance/Cashier/CostSheet` | 费用单 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Finance/ManageF/FinanceType` | 财务类型 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Finance/ManageF/WithdrawAsh` | 余额提现 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Finance/Cashier/CostTypes` | 费用类型 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/Finance/Supplier/SupplierPaymentList` | 供应商付款单列表 | 🔴 缺失 | 🔴 缺失 | 高 |

**文档覆盖率**：33%（6/18个功能有对应文档）
**完整度**：6%（仅1个文档完整）

### 8. 报表模块
| 功能路径 | 功能名称 | 对应文档 | 文档状态 | 优先级 |
|---------|---------|---------|---------|--------|
| `/statement/statementList/MerchandiseSalesStatement` | 商品销售报表 | `reports/goods-sales-report.md` | 🔴 缺失 | 高 |
| `/statement/statementList/CustomerMerchandiseReport` | 客户商品报表 | `reports/customer-goods-report.md` | 🔴 缺失 | 高 |
| `/statement/statementList/CustomerOrderReport` | 客户订单报表 | `reports/customer-order-report.md` | 🔴 缺失 | 高 |
| `/statement/statementList/OrderDataReport` | 订单数据报表 | `reports/order-data-report.md` | 🔴 缺失 | 高 |
| `/statement/statementList/RegionalOrderReport` | 地区订单报表 | `reports/regional-order-report.md` | 🔴 缺失 | 高 |
| `/statement/statementList/SaleRanking` | 业务员订单表 | `reports/salesman-order-report.md` | 🔴 缺失 | 高 |

**文档覆盖率**：100%（6/6个功能有对应文档文件）
**完整度**：0%（所有文档都是空白或内容极少）

### 9. 系统设置模块
| 功能路径 | 功能名称 | 对应文档 | 文档状态 | 优先级 |
|---------|---------|---------|---------|--------|
| `/SystemSettings/BaseSet` | 系统设置 | `system/base/settings.md` | 🟡 部分 | 中 |
| `/SystemSettings/jiaoyiset/voiceSet` | 语音设置 | `system/base/voice-settings.md` | 🟡 部分 | 中 |
| `/SystemSettings/jiaoyiset/PushNotification` | 消息推送 | `system/base/push-notification.md` | 🟡 部分 | 中 |
| `/SystemSettings/StepSet` | 流程设置 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/SystemSettings/jiaoyiset/PayList` | 支付设置 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/SystemSettings/jiaoyiset/Delivery` | 配送设置 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/SystemSettings/jiaoyiset/Driver` | 司机列表 | 🔴 缺失 | 🔴 缺失 | 中 |
| `/SystemSettings/jiaoyiset/Line` | 线路列表 | 🔴 缺失 | 🔴 缺失 | 中 |
| `/SystemSettings/liansuoguanli/ShopList` | 商铺列表 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/SystemSettings/accountAdmin/Department` | 部门管理 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/SystemSettings/accountAdmin/Role` | 角色管理 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/SystemSettings/accountAdmin/Staff` | 员工管理 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/SystemSettings/accountAdmin/LoginRecord` | 登录日志 | 🔴 缺失 | 🔴 缺失 | 中 |

**文档覆盖率**：23%（3/13个功能有对应文档）
**完整度**：0%（无完整文档）

### 10. 商城管理模块
| 功能路径 | 功能名称 | 对应文档 | 文档状态 | 优先级 |
|---------|---------|---------|---------|--------|
| `/ShoppingMall/AppDesign/PageDesignList` | 页面设计 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/ShoppingMall/AppDesign/StartUpPage` | 启动页 | 🔴 缺失 | 🔴 缺失 | 中 |
| `/ShoppingMall/AppDesign/AppStyle` | 风格设置 | 🔴 缺失 | 🔴 缺失 | 中 |
| `/ShoppingMall/AppDesign/CateSet` | 分类模版 | 🔴 缺失 | 🔴 缺失 | 中 |
| `/ShoppingMall/mendianset/NoticeLsit` | 公告设置 | 🔴 缺失 | 🔴 缺失 | 中 |
| `/ShoppingMall/WxCode/WxCodeSet` | 小程序设置 | 🔴 缺失 | 🔴 缺失 | 高 |
| `/ShoppingMall/WxCode/TemplateSet` | 小程序发布 | 🔴 缺失 | 🔴 缺失 | 高 |

**文档覆盖率**：0%（0/7个功能有对应文档）
**完整度**：0%

## 总体统计分析

### 按模块统计
| 模块 | 功能总数 | 有文档功能数 | 完整文档数 | 覆盖率 | 完整度 |
|------|---------|-------------|-----------|--------|--------|
| 概况 | 1 | 0 | 0 | 0% | 0% |
| 商品管理 | 10 | 7 | 1 | 70% | 10% |
| 订单管理 | 7 | 4 | 1 | 57% | 14% |
| 客户管理 | 12 | 12 | 1 | 100% | 8% |
| 采购管理 | 10 | 0 | 0 | 0% | 0% |
| 库存管理 | 16 | 16 | 0 | 100% | 0% |
| 财务管理 | 18 | 6 | 1 | 33% | 6% |
| 报表 | 6 | 6 | 0 | 100% | 0% |
| 系统设置 | 13 | 3 | 0 | 23% | 0% |
| 商城管理 | 7 | 0 | 0 | 0% | 0% |
| **总计** | **100** | **54** | **4** | **54%** | **4%** |

### 优先级分布
- **高优先级**（急需补充）：46个功能点
- **中优先级**（需要完善）：50个功能点
- **低优先级**（优化改进）：4个功能点

### 关键发现

1. **文档覆盖率中等**：54%的功能有对应文档，但质量参差不齐
2. **完整度极低**：仅4%的功能有完整的帮助文档
3. **严重缺失模块**：采购管理、商城管理完全没有文档
4. **部分完善模块**：客户管理、库存管理、报表模块有文档框架但内容不足

### 改进建议

#### 立即行动（高优先级）
1. **补充核心缺失模块**：
   - 概况模块：经营概况使用指南
   - 采购管理：完整的采购流程文档
   - 财务管理：应付管理和出纳功能
   - 系统设置：组织管理和权限设置
   - 商城管理：页面设计和小程序配置

2. **完善报表功能文档**：
   - 所有报表都有文件但内容空白，需要全面补充

#### 中期完善（中优先级）
1. **深化现有文档**：
   - 商品管理：补充各子功能的详细操作
   - 订单管理：完善销售报表功能
   - 库存管理：添加具体操作步骤
   - 客户管理：补充行为分析和报表功能

#### 长期优化（低优先级）
1. **优化已完整文档**：
   - 更新截图和示例
   - 完善交叉引用
   - 改进用户体验

---

*本映射关系表为后续文档补充工作提供精确指导*
