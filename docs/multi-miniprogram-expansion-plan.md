# 企业绑定多个微信小程序功能扩展技术方案

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-02
- **最后更新**: 2025-01-02
- **文档状态**: 草案
- **负责人**: 开发团队

## 1. 项目概述

### 1.1 背景
当前系统设计为一个SaaS企业账号只能绑定一个微信小程序，随着业务发展需要支持一个企业同时管理多个微信小程序，每个小程序可以绑定不同的默认店铺，实现更灵活的业务场景。

### 1.2 目标
- 支持一个企业管理多个微信小程序
- 每个小程序可以绑定一个默认店铺
- 保持现有功能的向后兼容性
- 提供完整的小程序管理界面
- 支持多小程序的代码提交、审核、发布流程

## 2. 当前系统架构分析

### 2.1 数据库层面现状

| 表名 | 作用 | 当前限制 |
|------|------|----------|
| qianniao_system | 存储企业小程序配置(type=1) | 一企业一配置 |
| qianniao_wx_bind_enterprise | 企业与微信小程序绑定关系 | 唯一索引限制一对一 |
| qianniao_enterprise | 企业基础信息，包含appid等字段 | 单一小程序字段 |
| qianniao_enterprise_bind_template | 企业绑定模板关系 | 按企业维度 |
| qianniao_shop | 店铺信息 | 已支持一企业多店铺 |

### 2.2 后端层面现状

**核心类分析：**
- `SystemCache::getAppIdByEnterpriseId()`: 获取企业唯一小程序配置
- `MEnterpriseBindTemplate`: 处理模板绑定、代码提交、审核发布
- `Oplatform`: 微信开放平台交互工具
- 缓存机制：Redis存储企业与小程序一对一映射

### 2.3 前端层面现状

**现有页面：**
- `WxCodeSet.vue`: 小程序基础设置，假设单一小程序
- `TemplateSet.vue`: 模板管理和发布流程，按企业维度操作
- 导航菜单：微信小程序设置入口单一

### 2.4 小程序端现状

**配置机制：**
- `ext.json`: 存储单一小程序的extConfig配置
- `modifyManifest.js`: 构建时修改配置，支持单一小程序
- 登录认证：基于企业维度，未区分具体小程序

## 3. 数据库层面修改方案

### 3.1 新增核心表

#### 3.1.1 企业小程序管理表
```sql
-- 企业小程序管理表
CREATE TABLE `qianniao_enterprise_miniprogram` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `enterpriseId` int(11) NOT NULL COMMENT '企业ID',
  `appid` varchar(50) NOT NULL COMMENT '小程序AppID',
  `appSecret` varchar(50) DEFAULT NULL COMMENT '小程序密钥',
  `name` varchar(100) NOT NULL COMMENT '小程序名称',
  `description` varchar(255) DEFAULT NULL COMMENT '小程序描述',
  `status` tinyint(3) DEFAULT '5' COMMENT '状态：4-禁用 5-启用',
  `isDefault` tinyint(3) DEFAULT '4' COMMENT '是否默认：4-否 5-是',
  `defaultShopId` int(11) DEFAULT NULL COMMENT '默认绑定店铺ID',
  `createTime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updateTime` int(10) DEFAULT NULL COMMENT '更新时间',
  `extends` json DEFAULT NULL COMMENT '扩展字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_enterprise_appid` (`enterpriseId`, `appid`),
  KEY `idx_enterprise_default` (`enterpriseId`, `isDefault`),
  KEY `idx_enterprise_status` (`enterpriseId`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='企业小程序管理表';
```

### 3.2 修改现有表结构

#### 3.2.1 系统设置表
```sql
-- 系统设置表增加小程序ID字段
ALTER TABLE `qianniao_system` 
ADD COLUMN `miniprogramId` int(11) DEFAULT NULL COMMENT '小程序ID' AFTER `enterpriseId`;

-- 添加索引
ALTER TABLE `qianniao_system` 
ADD KEY `idx_enterprise_miniprogram` (`enterpriseId`, `miniprogramId`);
```

#### 3.2.2 微信绑定企业表
```sql
-- 微信绑定企业表增加小程序ID字段
ALTER TABLE `qianniao_wx_bind_enterprise` 
ADD COLUMN `miniprogramId` int(11) DEFAULT NULL COMMENT '小程序ID' AFTER `enterpriseId`;

-- 删除原有唯一索引，添加新的复合唯一索引
ALTER TABLE `qianniao_wx_bind_enterprise` DROP INDEX `index`;
ALTER TABLE `qianniao_wx_bind_enterprise` 
ADD UNIQUE KEY `uk_enterprise_miniprogram` (`enterpriseId`, `miniprogramId`);
```

#### 3.2.3 企业绑定模板表
```sql
-- 企业绑定模板表增加小程序ID字段
ALTER TABLE `qianniao_enterprise_bind_template` 
ADD COLUMN `miniprogramId` int(11) DEFAULT NULL COMMENT '小程序ID' AFTER `enterpriseId`;

-- 添加索引
ALTER TABLE `qianniao_enterprise_bind_template` 
ADD KEY `idx_enterprise_miniprogram` (`enterpriseId`, `miniprogramId`);
```

### 3.3 数据迁移脚本

#### 3.3.1 迁移现有数据
```sql
-- 数据迁移脚本：将现有单小程序数据迁移到新架构

-- 1. 为现有企业创建默认小程序记录
INSERT INTO `qianniao_enterprise_miniprogram` 
(enterpriseId, appid, name, status, isDefault, createTime, updateTime)
SELECT 
    e.id as enterpriseId,
    COALESCE(JSON_UNQUOTE(JSON_EXTRACT(s.content, '$.appid')), e.appid) as appid,
    CONCAT(e.name, '-默认小程序') as name,
    5 as status,
    5 as isDefault,
    UNIX_TIMESTAMP() as createTime,
    UNIX_TIMESTAMP() as updateTime
FROM qianniao_enterprise e
LEFT JOIN qianniao_system s ON e.id = s.enterpriseId AND s.type = 1
WHERE e.deleteStatus = 5
AND (
    (s.content IS NOT NULL AND JSON_UNQUOTE(JSON_EXTRACT(s.content, '$.appid')) IS NOT NULL)
    OR e.appid IS NOT NULL
);

-- 2. 更新系统设置表的miniprogramId
UPDATE qianniao_system s
INNER JOIN qianniao_enterprise_miniprogram emp ON s.enterpriseId = emp.enterpriseId
SET s.miniprogramId = emp.id
WHERE s.type = 1 AND emp.isDefault = 5;

-- 3. 更新微信绑定企业表的miniprogramId
UPDATE qianniao_wx_bind_enterprise wbe
INNER JOIN qianniao_enterprise_miniprogram emp ON wbe.enterpriseId = emp.enterpriseId
SET wbe.miniprogramId = emp.id
WHERE emp.isDefault = 5;

-- 4. 更新企业绑定模板表的miniprogramId
UPDATE qianniao_enterprise_bind_template ebt
INNER JOIN qianniao_enterprise_miniprogram emp ON ebt.enterpriseId = emp.enterpriseId
SET ebt.miniprogramId = emp.id
WHERE emp.isDefault = 5;
```

## 4. 后端层面调整方案

### 4.1 新增Model类

#### 4.1.1 MEnterpriseMiniprogram.Class.php
```php
<?php
namespace JinDouYun\Model\System;

use JinDouYun\Dao\System\DEnterpriseMiniprogram;
use JinDouYun\Model\MBaseModel;
use Mall\Framework\Core\ErrorCode;
use Mall\Framework\Core\ResultWrapper;

class MEnterpriseMiniprogram extends MBaseModel
{
    private $objDEnterpriseMiniprogram;
    
    public function __construct($enterpriseId = 0, $userCenterId = 0)
    {
        parent::__construct($enterpriseId, $userCenterId);
        $this->objDEnterpriseMiniprogram = new DEnterpriseMiniprogram();
    }
    
    /**
     * 添加小程序
     */
    public function addMiniprogram($params)
    {
        // 验证AppID唯一性
        $exists = $this->objDEnterpriseMiniprogram->get([
            'enterpriseId' => $this->enterpriseId,
            'appid' => $params['appid']
        ]);
        
        if ($exists) {
            return ResultWrapper::fail('该AppID已存在', ErrorCode::$paramError);
        }
        
        $params['enterpriseId'] = $this->enterpriseId;
        
        // 如果是第一个小程序，自动设为默认
        $count = $this->objDEnterpriseMiniprogram->count(['enterpriseId' => $this->enterpriseId]);
        if ($count == 0) {
            $params['isDefault'] = 5;
        }
        
        $result = $this->objDEnterpriseMiniprogram->insert($params);
        if ($result === false) {
            return ResultWrapper::fail($this->objDEnterpriseMiniprogram->error(), ErrorCode::$dberror);
        }
        
        return ResultWrapper::success($result);
    }
    
    /**
     * 设置默认小程序
     */
    public function setDefaultMiniprogram($miniprogramId)
    {
        $this->objDEnterpriseMiniprogram->beginTransaction();
        
        try {
            // 取消当前默认小程序
            $this->objDEnterpriseMiniprogram->update(
                ['isDefault' => 4],
                ['enterpriseId' => $this->enterpriseId, 'isDefault' => 5]
            );
            
            // 设置新的默认小程序
            $result = $this->objDEnterpriseMiniprogram->update(
                ['isDefault' => 5],
                ['id' => $miniprogramId, 'enterpriseId' => $this->enterpriseId]
            );
            
            if ($result === false) {
                $this->objDEnterpriseMiniprogram->rollBack();
                return ResultWrapper::fail('设置失败', ErrorCode::$dberror);
            }
            
            $this->objDEnterpriseMiniprogram->commit();
            return ResultWrapper::success('设置成功');
            
        } catch (Exception $e) {
            $this->objDEnterpriseMiniprogram->rollBack();
            return ResultWrapper::fail($e->getMessage(), ErrorCode::$dberror);
        }
    }
    
    /**
     * 获取企业小程序列表
     */
    public function getMiniprogramList($params = [])
    {
        $where = ['enterpriseId' => $this->enterpriseId];
        
        if (isset($params['status'])) {
            $where['status'] = $params['status'];
        }
        
        $list = $this->objDEnterpriseMiniprogram->select($where, '*', 'isDefault DESC, createTime DESC');
        
        // 关联店铺信息
        foreach ($list as &$item) {
            if ($item['defaultShopId']) {
                // 获取店铺信息的逻辑
                $item['defaultShopName'] = $this->getShopName($item['defaultShopId']);
            }
        }
        
        return ResultWrapper::success($list);
    }
}
```

#### 4.1.2 DEnterpriseMiniprogram.Class.php
```php
<?php
namespace JinDouYun\Dao\System;

use JinDouYun\Dao\BaseDao;

class DEnterpriseMiniprogram extends BaseDao
{
    public function __construct($serviceDB = 'default')
    {
        $this->_table = 'enterprise_miniprogram';
        $this->_primary = 'id';
        $this->_fields = [
            "id", "enterpriseId", "appid", "appSecret", "name", "description",
            "status", "isDefault", "defaultShopId", "createTime", "updateTime", "extends"
        ];
        $this->_readonly = ['id'];
        $this->_create_autofill = ['createTime' => time()];
        $this->_update_autofill = ['updateTime' => time()];

        parent::__construct($serviceDB);
    }
}
```

### 4.2 修改现有Model类

#### 4.2.1 SystemCache类调整
```php
class SystemCache
{
    private static $miniprogramConfigKey = 'miniprogramConfigMapping';
    private static $enterpriseMiniprogramsKey = 'enterpriseMiniprogramsMapping';

    /**
     * 缓存小程序配置
     */
    public function cacheMiniprogramConfig($miniprogramId, $config)
    {
        return $this->cache->hset(self::$miniprogramConfigKey, $miniprogramId, json_encode($config));
    }

    /**
     * 获取小程序配置
     */
    public function getMiniprogramConfig($miniprogramId)
    {
        $config = $this->cache->hget(self::$miniprogramConfigKey, $miniprogramId);
        return $config ? json_decode($config, true) : null;
    }

    /**
     * 缓存企业小程序列表
     */
    public function cacheEnterpriseMiniprograms($enterpriseId, $miniprograms)
    {
        return $this->cache->hset(self::$enterpriseMiniprogramsKey, $enterpriseId, json_encode($miniprograms));
    }

    /**
     * 获取企业默认小程序
     */
    public function getDefaultMiniprogram($enterpriseId)
    {
        $miniprograms = $this->cache->hget(self::$enterpriseMiniprogramsKey, $enterpriseId);
        if ($miniprograms) {
            $list = json_decode($miniprograms, true);
            foreach ($list as $miniprogram) {
                if ($miniprogram['isDefault'] == 5) {
                    return $miniprogram;
                }
            }
        }
        return null;
    }

    /**
     * 兼容原有方法，获取企业小程序配置
     */
    public function getAppIdByEnterpriseId($enterpriseId)
    {
        $defaultMiniprogram = $this->getDefaultMiniprogram($enterpriseId);
        if ($defaultMiniprogram) {
            return json_encode([
                'appid' => $defaultMiniprogram['appid'],
                'appSecret' => $defaultMiniprogram['appSecret']
            ]);
        }

        // 兼容旧数据
        return $this->cache->hget(self::$enterpriseRelationWxAppIdKey, $enterpriseId);
    }
}
```

#### 4.2.2 MEnterpriseBindTemplate类调整
```php
class MEnterpriseBindTemplate extends MBaseModel
{
    /**
     * 提交代码 - 支持指定小程序
     */
    public function submitCode($params, $token, $miniprogramId = null)
    {
        $weixinTemplateId = $params['weixinTemplateId'];
        unset($params['weixinTemplateId']);

        // 获取小程序配置
        $miniprogramSetting = $this->getMiniprogramSetting($miniprogramId);
        if (empty($miniprogramSetting)) {
            return ResultWrapper::fail('小程序配置项为空', ErrorCode::$configEroor);
        }

        $authorizer_appid = $miniprogramSetting['appid'];

        $ext_json = [
            'extEnable'    => 'true',
            'extAppid'     => $miniprogramSetting['appid'],
            'directCommit' => false,
            'ext'          => [
                'token' => $token,
                'miniprogramId' => $miniprogramId,
                'defaultShopId' => $miniprogramSetting['defaultShopId'] ?? null
            ],
        ];

        // 其余逻辑保持不变...
    }

    /**
     * 获取小程序配置
     */
    private function getMiniprogramSetting($miniprogramId = null)
    {
        if ($miniprogramId) {
            $objSystemCache = new SystemCache();
            return $objSystemCache->getMiniprogramConfig($miniprogramId);
        } else {
            // 兼容原有逻辑，获取默认小程序
            $objSystemCache = new SystemCache();
            return $objSystemCache->getAppIdByEnterpriseId($this->onlineEnterpriseId);
        }
    }
}
```

### 4.3 新增Controller类

#### 4.3.1 EnterpriseMiniprogram.Class.php
```php
<?php
namespace JinDouYun\Controller\System;

use JinDouYun\Controller\BaseController;
use JinDouYun\Model\System\MEnterpriseMiniprogram;
use Mall\Framework\Core\ErrorCode;

class EnterpriseMiniprogram extends BaseController
{
    private $objMEnterpriseMiniprogram;

    public function __construct($isCheckAcl = true, $isMustLogin = true)
    {
        parent::__construct($isCheckAcl, $isMustLogin);
        $this->objMEnterpriseMiniprogram = new MEnterpriseMiniprogram($this->onlineEnterpriseId, $this->onlineUserId);
    }

    /**
     * 获取小程序列表
     */
    public function getMiniprogramList()
    {
        $params = $this->request->getRawJson();
        $result = $this->objMEnterpriseMiniprogram->getMiniprogramList($params);

        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        } else {
            parent::sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 添加小程序
     */
    public function addMiniprogram()
    {
        $params = $this->request->getRawJson();

        if (empty($params['name']) || empty($params['appid'])) {
            parent::sendOutput('参数不完整', ErrorCode::$paramError);
        }

        $result = $this->objMEnterpriseMiniprogram->addMiniprogram($params);

        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        } else {
            parent::sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 设置默认小程序
     */
    public function setDefaultMiniprogram()
    {
        $miniprogramId = $this->request->param('request_id');

        if (empty($miniprogramId)) {
            parent::sendOutput('参数错误', ErrorCode::$paramError);
        }

        $result = $this->objMEnterpriseMiniprogram->setDefaultMiniprogram($miniprogramId);

        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        } else {
            parent::sendOutput($result->getData(), $result->getErrorCode());
        }
    }
}
```

## 5. 前端层面调整方案

### 5.1 新增管理页面

#### 5.1.1 MiniprogramManagement.vue
```vue
<template>
  <ContainerTit>
    <Container>
      <div class="miniprogram-management">
        <!-- 页面标题 -->
        <div class="page-header">
          <h2>小程序管理</h2>
          <el-button
            v-if="$accessCheck($Access.AddMiniprogram)"
            type="primary"
            @click="showAddDialog"
          >
            添加小程序
          </el-button>
        </div>

        <!-- 小程序列表 -->
        <el-table :data="miniprogramList" border>
          <el-table-column prop="name" label="小程序名称" width="200"/>
          <el-table-column prop="appid" label="AppID" width="200"/>
          <el-table-column prop="defaultShopName" label="默认店铺" width="150"/>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 5 ? 'success' : 'danger'">
                {{ scope.row.status === 5 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="isDefault" label="默认小程序" width="120">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.isDefault === 5" type="primary">默认</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="300">
            <template slot-scope="scope">
              <el-button
                size="mini"
                @click="editMiniprogram(scope.row)"
                v-if="$accessCheck($Access.EditMiniprogram)"
              >
                编辑
              </el-button>
              <el-button
                size="mini"
                type="primary"
                @click="setDefault(scope.row)"
                v-if="scope.row.isDefault !== 5 && $accessCheck($Access.SetDefaultMiniprogram)"
              >
                设为默认
              </el-button>
              <el-button
                size="mini"
                @click="manageTemplate(scope.row)"
                v-if="$accessCheck($Access.TemplateManagement)"
              >
                模板管理
              </el-button>
              <el-button
                size="mini"
                type="danger"
                @click="deleteMiniprogram(scope.row)"
                v-if="scope.row.isDefault !== 5 && $accessCheck($Access.DeleteMiniprogram)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 添加/编辑对话框 -->
        <MiniprogramForm
          :visible.sync="dialogVisible"
          :form-data="currentMiniprogram"
          :shop-list="shopList"
          @submit="handleSubmit"
        />
      </div>
    </Container>
  </ContainerTit>
</template>

<script>
import MiniprogramForm from './MiniprogramForm.vue';
import {
  getMiniprogramList,
  addMiniprogram,
  editMiniprogram,
  deleteMiniprogram,
  setDefaultMiniprogram
} from '@/api/System';

export default {
  name: 'MiniprogramManagement',
  components: {
    MiniprogramForm
  },
  data() {
    return {
      miniprogramList: [],
      shopList: [],
      dialogVisible: false,
      currentMiniprogram: {},
      isEdit: false
    };
  },
  created() {
    this.getMiniprogramList();
    this.getShopList();
  },
  methods: {
    async getMiniprogramList() {
      try {
        const { data } = await getMiniprogramList();
        this.miniprogramList = data;
      } catch (error) {
        this.$message.error('获取小程序列表失败');
      }
    },

    showAddDialog() {
      this.currentMiniprogram = {};
      this.isEdit = false;
      this.dialogVisible = true;
    },

    editMiniprogram(row) {
      this.currentMiniprogram = { ...row };
      this.isEdit = true;
      this.dialogVisible = true;
    },

    async setDefault(row) {
      try {
        await setDefaultMiniprogram(row.id);
        this.$message.success('设置成功');
        this.getMiniprogramList();
      } catch (error) {
        this.$message.error('设置失败');
      }
    },

    manageTemplate(row) {
      this.$router.push({
        path: '/ShoppingMall/WxCode/TemplateSet',
        query: { miniprogramId: row.id }
      });
    },

    async handleSubmit(formData) {
      try {
        if (this.isEdit) {
          await editMiniprogram(formData.id, formData);
        } else {
          await addMiniprogram(formData);
        }
        this.$message.success('操作成功');
        this.dialogVisible = false;
        this.getMiniprogramList();
      } catch (error) {
        this.$message.error('操作失败');
      }
    }
  }
};
</script>
```

#### 5.1.2 MiniprogramForm.vue
```vue
<template>
  <el-dialog
    :title="formData.id ? '编辑小程序' : '添加小程序'"
    :visible.sync="dialogVisible"
    width="600px"
    @close="handleClose"
  >
    <el-form :model="form" :rules="rules" ref="form" label-width="120px">
      <el-form-item label="小程序名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入小程序名称"/>
      </el-form-item>

      <el-form-item label="AppID" prop="appid">
        <el-input v-model="form.appid" placeholder="请输入小程序AppID"/>
      </el-form-item>

      <el-form-item label="AppSecret" prop="appSecret">
        <el-input v-model="form.appSecret" placeholder="请输入小程序密钥"/>
      </el-form-item>

      <el-form-item label="小程序描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入小程序描述"
        />
      </el-form-item>

      <el-form-item label="默认店铺" prop="defaultShopId">
        <el-select v-model="form.defaultShopId" placeholder="请选择默认店铺">
          <el-option
            v-for="shop in shopList"
            :key="shop.id"
            :label="shop.name"
            :value="shop.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态">
        <el-radio-group v-model="form.status">
          <el-radio :label="5">启用</el-radio>
          <el-radio :label="4">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'MiniprogramForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    shopList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        name: '',
        appid: '',
        appSecret: '',
        description: '',
        defaultShopId: null,
        status: 5
      },
      rules: {
        name: [
          { required: true, message: '请输入小程序名称', trigger: 'blur' }
        ],
        appid: [
          { required: true, message: '请输入小程序AppID', trigger: 'blur' }
        ],
        appSecret: [
          { required: true, message: '请输入小程序密钥', trigger: 'blur' }
        ],
        defaultShopId: [
          { required: true, message: '请选择默认店铺', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  watch: {
    formData: {
      handler(newVal) {
        this.form = { ...this.form, ...newVal };
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', { ...this.form });
        }
      });
    },

    handleClose() {
      this.$refs.form.resetFields();
      this.dialogVisible = false;
    }
  }
};
</script>
```

### 5.2 修改现有页面

#### 5.2.1 WxCodeSet.vue调整
```vue
<template>
  <ContainerTit>
    <Container>
      <!-- 小程序选择器 -->
      <div class="miniprogram-selector" v-if="miniprogramList.length > 1">
        <el-form-item label="选择小程序">
          <el-select v-model="selectedMiniprogramId" @change="handleMiniprogramChange">
            <el-option
              v-for="miniprogram in miniprogramList"
              :key="miniprogram.id"
              :label="miniprogram.name"
              :value="miniprogram.id"
            />
          </el-select>
        </el-form-item>
      </div>

      <!-- 原有的小程序配置表单 -->
      <el-form :model="mallForm" ref="mallForm">
        <!-- 原有表单项保持不变 -->
      </el-form>
    </Container>
  </ContainerTit>
</template>

<script>
import { getMiniprogramList, getSystemSettingsInfo, updateSystemSettings } from "@/api/System";

export default {
  name: "WxCodeSet",
  data() {
    return {
      miniprogramList: [],
      selectedMiniprogramId: null,
      mallForm: {
        // 原有表单数据
      }
    };
  },
  created() {
    this.getMiniprogramList();
  },
  methods: {
    async getMiniprogramList() {
      try {
        const { data } = await getMiniprogramList();
        this.miniprogramList = data;

        // 默认选择第一个或默认小程序
        const defaultMiniprogram = data.find(item => item.isDefault === 5) || data[0];
        if (defaultMiniprogram) {
          this.selectedMiniprogramId = defaultMiniprogram.id;
          this.getSystemSettingsInfo(1);
        }
      } catch (error) {
        this.$message.error('获取小程序列表失败');
      }
    },

    handleMiniprogramChange() {
      this.getSystemSettingsInfo(1);
    },

    async getSystemSettingsInfo(type) {
      if (!this.selectedMiniprogramId) return;

      const { data } = await getSystemSettingsInfo(type, this.selectedMiniprogramId);
      this.mallForm = {
        ...data,
        ...this.mallForm,
        ...data.content,
      };
    },

    async updateSystemSettings(type, form) {
      const params = {
        type,
        miniprogramId: this.selectedMiniprogramId,
        content: { ...form }
      };

      await updateSystemSettings(this.mallForm.id, params);
      this.$message.success("提交成功");
      this.getSystemSettingsInfo(type);
    }
  }
};
</script>
```

#### 5.2.2 TemplateSet.vue调整
```vue
<template>
  <ContainerTit>
    <Container>
      <!-- 小程序选择器 -->
      <div class="miniprogram-selector" v-if="miniprogramList.length > 1">
        <el-form-item label="选择小程序">
          <el-select v-model="selectedMiniprogramId" @change="handleMiniprogramChange">
            <el-option
              v-for="miniprogram in miniprogramList"
              :key="miniprogram.id"
              :label="miniprogram.name"
              :value="miniprogram.id"
            />
          </el-select>
        </el-form-item>
      </div>

      <!-- 原有的模板管理流程 -->
      <el-timeline>
        <!-- 原有步骤保持不变，但操作时传递selectedMiniprogramId -->
      </el-timeline>
    </Container>
  </ContainerTit>
</template>

<script>
export default {
  name: "TemplateSet",
  data() {
    return {
      miniprogramList: [],
      selectedMiniprogramId: null,
      // 原有数据
    };
  },
  created() {
    this.getMiniprogramList();
    // 检查路由参数中是否指定了小程序ID
    if (this.$route.query.miniprogramId) {
      this.selectedMiniprogramId = parseInt(this.$route.query.miniprogramId);
    }
  },
  methods: {
    // 所有原有方法增加miniprogramId参数
    async submitCode(row) {
      const data = await submitCode({
        templateId: row.templateId,
        miniprogramId: this.selectedMiniprogramId
      });
      // 其余逻辑不变
    },

    async submitAudit(row) {
      const data = await submitAudit({
        templateId: row.templateId,
        miniprogramId: this.selectedMiniprogramId
      });
      // 其余逻辑不变
    }
  }
};
</script>
```

## 6. 小程序端适配方案

### 6.1 extConfig配置调整

#### 6.1.1 modifyManifest.js增强
```javascript
const fs = require('fs');

// 修改manifest.json
const manifestPath = process.env.UNI_INPUT_DIR + '/manifest.json';
fs.readFile(manifestPath, function(err, data) {
    if (err) {
        console.error(err);
    } else {
        var _data = JSON.parse(data.toString());
        _data['mp-weixin'].appid = process.UNI_SCRIPT_ENV.APPID;

        // 支持多小程序插件配置
        if (process.UNI_SCRIPT_DEFINE['MP-WEIXIN-PIFA-DEV'] || process.UNI_SCRIPT_DEFINE['MP-WEIXIN-PIFA-PRO']) {
            _data['mp-weixin'].plugins = {
                "bb-plugin": {
                    version: "latest",
                    provider: "wx69b7451feb427f0e"
                }
            };
        } else {
            _data['mp-weixin'].plugins = {};
        }

        _data = JSON.stringify(_data, null, 4);
        fs.writeFile(manifestPath, _data, { encoding: "utf-8" }, function(err) {
            if (err) {
                console.log('写入失败', err);
            } else {
                console.log('manifest.json写入成功');
            }
        });
    }
});

// 修改ext.json - 支持多小程序配置
const extPath = process.env.UNI_INPUT_DIR + '/ext.json';
fs.readFile(extPath, function(err, data) {
    if (err) {
        console.error(err);
    } else {
        var _data = JSON.parse(data.toString());
        _data['extAppid'] = process.UNI_SCRIPT_ENV.APPID;
        _data['ext'].token = process.UNI_SCRIPT_ENV.TOKEN;

        // 新增多小程序支持字段
        _data['ext'].miniprogramId = process.UNI_SCRIPT_ENV.MINIPROGRAM_ID;
        _data['ext'].defaultShopId = process.UNI_SCRIPT_ENV.DEFAULT_SHOP_ID;

        _data = JSON.stringify(_data, null, 4);
        fs.writeFile(extPath, _data, { encoding: "utf-8" }, function(err) {
            if (err) {
                console.log('写入失败', err);
            } else {
                console.log('ext.json写入成功');
            }
        });
    }
});
```

### 6.2 登录认证流程调整

#### 6.2.1 wxLogin.vue增强
```vue
<script>
export default {
  methods: {
    // 检查小程序绑定状态
    async checkBindMiniProgram(openid, unionId) {
      const params = {
        openid,
        unionId,
        appid: this.$common.getAppId(), // 当前小程序AppID
        source: this.$common.source()
      };

      try {
        const result = await this.$u.api.checkMiniprogramBinding(params);

        if (result.data.isBinding) {
          // 已绑定，设置默认店铺
          if (result.data.defaultShopId) {
            this.$store.commit('commit_nowStore', result.data.shopInfo);
          }

          // 设置小程序信息
          this.$store.commit('commit_miniprogramInfo', {
            id: result.data.miniprogramId,
            name: result.data.miniprogramName,
            defaultShopId: result.data.defaultShopId
          });

          this.goPage('/pages/index/index', 'switchTab');
        } else {
          // 未绑定，引导绑定
          this.showBindingDialog();
        }
      } catch (error) {
        console.error('检查小程序绑定状态失败:', error);
        this.$api.msg('登录失败，请重试');
      }
    },

    // 小程序绑定
    async bindMiniprogram(params) {
      const bindParams = {
        ...params,
        appid: this.$common.getAppId(),
        miniprogramId: this.getMiniprogramId()
      };

      const result = await this.$u.api.bindMiniprogram(bindParams);

      if (result.success) {
        this.$api.msg('绑定成功');
        // 重新检查绑定状态
        this.checkBindMiniProgram(params.openId, params.unionId);
      }
    },

    // 获取当前小程序ID
    getMiniprogramId() {
      // 从ext配置中获取
      if (typeof __wxConfig !== 'undefined' && __wxConfig.ext) {
        return __wxConfig.ext.miniprogramId;
      }
      return null;
    }
  }
};
</script>
```

## 7. 微信开放平台交互调整

### 7.1 Oplatform类增强

```php
class Oplatform
{
    /**
     * 批量提交代码
     */
    public function batchCommit($miniprogramConfigs)
    {
        $results = [];

        foreach ($miniprogramConfigs as $config) {
            $result = $this->commit(
                $config['appid'],
                $config['templateId'],
                $config['ext_json'],
                $config['version'],
                $config['description']
            );

            $results[$config['appid']] = [
                'success' => $result->isSuccess(),
                'data' => $result->getData(),
                'errorCode' => $result->getErrorCode()
            ];
        }

        return ResultWrapper::success($results);
    }

    /**
     * 批量查询审核状态
     */
    public function batchGetAuditStatus($appids)
    {
        $results = [];

        foreach ($appids as $appid) {
            $result = $this->getAuditStatus($appid);
            $results[$appid] = [
                'success' => $result->isSuccess(),
                'data' => $result->getData()
            ];
        }

        return ResultWrapper::success($results);
    }

    /**
     * 批量发布小程序
     */
    public function batchRelease($appids)
    {
        $results = [];

        foreach ($appids as $appid) {
            $result = $this->release($appid);
            $results[$appid] = [
                'success' => $result->isSuccess(),
                'data' => $result->getData()
            ];
        }

        return ResultWrapper::success($results);
    }
}
```

## 8. 详细实施步骤和时间规划

### 8.1 项目阶段划分

| 阶段 | 任务内容 | 预计工期 | 负责人 | 依赖关系 |
|------|----------|----------|--------|----------|
| 第一阶段 | 数据库设计和基础架构 | 3天 | 后端开发 | 无 |
| 第二阶段 | 后端API开发 | 5天 | 后端开发 | 第一阶段完成 |
| 第三阶段 | 前端界面开发 | 4天 | 前端开发 | 第二阶段完成 |
| 第四阶段 | 小程序端适配 | 2天 | 前端开发 | 第二阶段完成 |
| 第五阶段 | 集成测试 | 3天 | 全体开发 | 前三阶段完成 |
| 第六阶段 | 上线部署 | 1天 | 运维+开发 | 第五阶段完成 |

**总工期：18天**

### 8.2 详细实施计划

#### 第一阶段：数据库设计和基础架构（3天）

**Day 1: 数据库设计**
- [ ] 创建`qianniao_enterprise_miniprogram`表
- [ ] 修改现有表结构，增加`miniprogramId`字段
- [ ] 编写数据迁移脚本
- [ ] 在测试环境执行迁移脚本

**Day 2: 数据迁移和验证**
- [ ] 执行数据迁移，将现有数据迁移到新架构
- [ ] 验证数据迁移的正确性
- [ ] 创建必要的数据库索引
- [ ] 编写回滚脚本

**Day 3: 基础架构调整**
- [ ] 创建`DEnterpriseMiniprogram` DAO类
- [ ] 调整缓存机制，支持多小程序
- [ ] 编写基础的单元测试

#### 第二阶段：后端API开发（5天）

**Day 4-5: Model层开发**
- [ ] 开发`MEnterpriseMiniprogram`类
- [ ] 修改`SystemCache`类，支持多小程序缓存
- [ ] 调整`MEnterpriseBindTemplate`类
- [ ] 编写Model层单元测试

**Day 6-7: Controller层开发**
- [ ] 开发`EnterpriseMiniprogram` Controller
- [ ] 修改现有Controller，支持多小程序参数
- [ ] 实现批量操作接口
- [ ] 编写API文档

**Day 8: 微信开放平台集成**
- [ ] 增强`Oplatform`类，支持批量操作
- [ ] 调整微信开放平台交互逻辑
- [ ] 测试微信API集成

#### 第三阶段：前端界面开发（4天）

**Day 9-10: 新增管理页面**
- [ ] 开发`MiniprogramManagement.vue`页面
- [ ] 开发`MiniprogramForm.vue`组件
- [ ] 实现小程序的增删改查功能
- [ ] 添加权限控制

**Day 11-12: 修改现有页面**
- [ ] 调整`WxCodeSet.vue`，支持多小程序选择
- [ ] 调整`TemplateSet.vue`，支持多小程序操作
- [ ] 更新导航菜单和路由配置
- [ ] 前端功能测试

#### 第四阶段：小程序端适配（2天）

**Day 13: 构建配置调整**
- [ ] 增强`modifyManifest.js`，支持多小程序配置
- [ ] 调整`ext.json`配置机制
- [ ] 更新构建脚本

**Day 14: 登录认证调整**
- [ ] 修改`wxLogin.vue`，支持多小程序认证
- [ ] 实现默认店铺绑定逻辑
- [ ] 小程序端功能测试

#### 第五阶段：集成测试（3天）

**Day 15: 功能测试**
- [ ] 完整的功能测试流程
- [ ] 多小程序场景测试
- [ ] 兼容性测试（确保单小程序企业正常）

**Day 16: 性能测试**
- [ ] 缓存性能测试
- [ ] 数据库查询性能测试
- [ ] 并发操作测试

**Day 17: 安全测试**
- [ ] 权限控制测试
- [ ] 数据隔离测试
- [ ] 安全漏洞扫描

#### 第六阶段：上线部署（1天）

**Day 18: 生产环境部署**
- [ ] 生产环境数据库迁移
- [ ] 代码部署
- [ ] 功能验证
- [ ] 监控配置

### 8.3 里程碑检查点

| 里程碑 | 检查内容 | 完成标准 |
|--------|----------|----------|
| M1 | 数据库迁移完成 | 所有表结构创建完成，数据迁移无误 |
| M2 | 后端API开发完成 | 所有API接口开发完成，单元测试通过 |
| M3 | 前端界面开发完成 | 所有页面开发完成，功能测试通过 |
| M4 | 小程序端适配完成 | 小程序配置和认证流程调整完成 |
| M5 | 集成测试完成 | 所有测试用例通过，性能达标 |
| M6 | 生产环境上线 | 功能正常运行，监控正常 |

## 9. 关键注意事项和风险点

### 9.1 技术风险

#### 9.1.1 数据一致性风险
**风险描述：** 多小程序间的数据隔离和共享可能导致数据不一致

**防范措施：**
- 设计严格的数据访问权限控制
- 实现事务性操作，确保数据一致性
- 建立数据校验机制
- 定期进行数据一致性检查

#### 9.1.2 性能风险
**风险描述：** 多小程序查询可能导致性能下降

**防范措施：**
- 优化数据库索引设计
- 实现多级缓存机制
- 使用分页查询，避免大数据量查询
- 监控关键性能指标

#### 9.1.3 缓存一致性风险
**风险描述：** 多小程序缓存更新可能出现不一致

**防范措施：**
- 设计合理的缓存失效策略
- 实现缓存版本控制
- 建立缓存监控和告警机制
- 提供缓存手动刷新功能

### 9.2 业务风险

#### 9.2.1 向后兼容性风险
**风险描述：** 新架构可能影响现有单小程序企业的功能

**防范措施：**
- 保留原有API接口的兼容性
- 实现平滑的数据迁移
- 充分的回归测试
- 制定回滚方案

#### 9.2.2 用户体验风险
**风险描述：** 界面复杂度增加可能影响用户体验

**防范措施：**
- 设计直观的用户界面
- 提供详细的操作指南
- 实现智能默认选择
- 收集用户反馈并持续优化

### 9.3 运维风险

#### 9.3.1 部署风险
**风险描述：** 数据库迁移和代码部署可能出现问题

**防范措施：**
- 在测试环境充分验证
- 制定详细的部署计划
- 准备完整的回滚方案
- 实施灰度发布策略

#### 9.3.2 监控风险
**风险描述：** 新功能可能缺乏有效监控

**防范措施：**
- 建立完善的监控体系
- 设置关键指标告警
- 实现日志记录和分析
- 定期进行健康检查

### 9.4 安全风险

#### 9.4.1 权限控制风险
**风险描述：** 多小程序权限控制可能存在漏洞

**防范措施：**
- 实现细粒度的权限控制
- 定期进行安全审计
- 建立权限变更审批流程
- 实施最小权限原则

#### 9.4.2 数据泄露风险
**风险描述：** 不同小程序间可能存在数据泄露

**防范措施：**
- 实现严格的数据隔离
- 加强API访问控制
- 实施数据加密存储
- 建立安全事件响应机制

### 9.5 风险应对策略

| 风险等级 | 应对策略 | 负责人 | 检查频率 |
|----------|----------|--------|----------|
| 高风险 | 立即处理，制定专项方案 | 项目经理 | 每日检查 |
| 中风险 | 优先处理，纳入开发计划 | 技术负责人 | 每周检查 |
| 低风险 | 持续关注，适时处理 | 开发人员 | 每月检查 |

## 10. 成功标准和验收条件

### 10.1 功能验收标准

- [ ] 企业可以添加、编辑、删除多个小程序
- [ ] 可以设置默认小程序和默认店铺绑定
- [ ] 支持按小程序维度进行模板管理和发布
- [ ] 小程序端可以正确识别当前小程序并绑定默认店铺
- [ ] 现有单小程序企业功能保持正常

### 10.2 性能验收标准

- [ ] 小程序列表查询响应时间 < 500ms
- [ ] 小程序配置保存响应时间 < 1s
- [ ] 缓存命中率 > 90%
- [ ] 数据库查询优化，避免N+1问题

### 10.3 安全验收标准

- [ ] 不同企业的小程序数据完全隔离
- [ ] 权限控制正确，无越权访问
- [ ] 敏感数据加密存储
- [ ] 通过安全扫描测试

### 10.4 兼容性验收标准

- [ ] 现有API接口保持兼容
- [ ] 现有前端页面正常工作
- [ ] 数据迁移完整无误
- [ ] 支持平滑回滚

## 11. 后续优化建议

### 11.1 功能增强
- 支持小程序模板的批量操作
- 实现小程序使用情况的统计分析
- 支持小程序间的数据同步功能
- 增加小程序性能监控功能

### 11.2 技术优化
- 实现更智能的缓存策略
- 优化数据库查询性能
- 支持小程序配置的版本管理
- 实现自动化测试覆盖

### 11.3 用户体验优化
- 提供更直观的小程序管理界面
- 实现拖拽排序功能
- 支持批量操作的进度显示
- 增加操作向导和帮助文档

---

**文档结束**

> 本文档为企业绑定多个微信小程序功能扩展的完整技术方案，包含了从数据库设计到前端实现的所有技术细节。请开发团队严格按照本方案执行，确保项目的顺利实施。
