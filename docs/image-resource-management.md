# 文档图片资源管理规范

## 现状分析

### 当前图片资源状况
- **总体数量**：约500+张图片文件
- **存储位置**：`help-docs/src/assets/` 目录
- **组织方式**：按功能模块分目录存储
- **命名方式**：部分规范，但存在不一致性
- **格式类型**：主要为PNG格式，少量JPG和其他格式

### 存在的问题

#### 1. 命名不规范
- **不一致的命名风格**：
  - `customer-list-main.png` (kebab-case)
  - `customer_list_main.png` (snake_case)
  - `customerListMain.png` (camelCase)
- **缺少统一的命名约定**
- **部分文件名不够描述性**

#### 2. 目录结构混乱
- **层级不一致**：有些功能有子目录，有些直接放在根目录
- **重复目录**：如 `customer` 和 `customer-list` 同时存在
- **命名不统一**：目录名与功能名不对应

#### 3. 文件管理问题
- **冗余文件**：存在未使用的图片文件
- **版本混乱**：同一功能的不同版本图片共存
- **格式不统一**：PNG、JPG混用，压缩程度不一

## 图片资源管理规范

### 1. 目录结构规范

#### 1.1 标准目录结构
```
src/assets/
├── overview/                    # 概况模块
│   ├── dashboard/              # 经营概况
│   └── quick-start/            # 快速入门
├── goods/                      # 商品管理
│   ├── manage/                 # 商品管理
│   │   ├── base-data/         # 商品资料
│   │   ├── classify/          # 商品分类
│   │   ├── brand/             # 品牌管理
│   │   └── attributes/        # 属性管理
│   └── price/                 # 价格管理
│       ├── price-table/       # 价格表
│       └── adjust-price/      # 调价单
├── order/                     # 订单管理
│   ├── manage/                # 订单管理
│   │   ├── order-list/        # 订单列表
│   │   ├── self-pickup/       # 自提单
│   │   ├── return-order/      # 退货单
│   │   └── proxy-order/       # 代客下单
│   └── reports/               # 销售报表
├── customer/                  # 客户管理
│   ├── manage/                # 客户管理
│   ├── check/                 # 客户审核
│   ├── behavior/              # 客户行为
│   └── reports/               # 客户报表
├── purchase/                  # 采购管理
│   ├── manage/                # 采购管理
│   └── reports/               # 采购报表
├── stock/                     # 库存管理
│   ├── in-out/                # 出入库
│   ├── inventory/             # 库存查询
│   ├── warehouse/             # 仓库管理
│   └── supplier/              # 供应商库存
├── finance/                   # 财务管理
│   ├── receivable/            # 应收管理
│   ├── payable/               # 应付管理
│   ├── cashier/               # 出纳管理
│   └── management/            # 财务管理
├── reports/                   # 报表模块
│   ├── goods/                 # 商品报表
│   └── order/                 # 订单报表
├── marketing/                 # 营销模块
│   ├── coupon/                # 优惠券
│   ├── promotion/             # 促销活动
│   ├── vip/                   # 会员管理
│   └── distribution/          # 分销管理
├── system/                    # 系统设置
│   ├── base/                  # 基础设置
│   ├── organization/          # 组织管理
│   ├── shop-decoration/       # 店铺装修
│   └── wechat-mp/             # 微信小程序
└── common/                    # 通用图片
    ├── icons/                 # 图标
    ├── logos/                 # 标志
    └── backgrounds/           # 背景图
```

#### 1.2 目录命名规则
- **使用kebab-case**：全小写，单词间用连字符分隔
- **英文命名**：使用英文单词，避免拼音
- **简洁明确**：目录名应简洁且能准确描述功能
- **层级合理**：最多3级目录，避免过深嵌套

### 2. 文件命名规范

#### 2.1 命名格式
```
[功能模块]-[具体功能]-[用途]-[状态].[扩展名]

示例：
goods-base-data-main.png           # 商品资料主界面
goods-base-data-add-form.png       # 商品资料添加表单
goods-base-data-edit-dialog.png    # 商品资料编辑对话框
goods-base-data-list-empty.png     # 商品资料列表空状态
```

#### 2.2 命名组成部分

**功能模块**（必需）：
- `overview` - 概况
- `goods` - 商品
- `order` - 订单
- `customer` - 客户
- `purchase` - 采购
- `stock` - 库存
- `finance` - 财务
- `reports` - 报表
- `marketing` - 营销
- `system` - 系统

**具体功能**（必需）：
- 对应具体的功能页面或模块
- 使用kebab-case命名

**用途**（必需）：
- `main` - 主界面/列表页
- `detail` - 详情页
- `form` - 表单页面
- `dialog` - 对话框/弹窗
- `step1/step2` - 分步操作
- `result` - 结果页面
- `error` - 错误状态
- `empty` - 空状态
- `loading` - 加载状态

**状态**（可选）：
- `active` - 激活状态
- `disabled` - 禁用状态
- `selected` - 选中状态
- `hover` - 悬停状态

#### 2.3 特殊用途命名

**按钮和控件**：
```
[功能]-[控件类型]-[状态].png

示例：
goods-add-button.png              # 添加按钮
goods-search-input.png            # 搜索输入框
goods-filter-dropdown.png         # 筛选下拉框
```

**流程步骤**：
```
[功能]-[流程名]-step[数字].png

示例：
goods-create-step1.png            # 商品创建第1步
goods-create-step2.png            # 商品创建第2步
```

**状态展示**：
```
[功能]-[状态描述].png

示例：
order-status-pending.png          # 订单待处理状态
order-status-completed.png        # 订单已完成状态
```

### 3. 图片质量规范

#### 3.1 技术规格
- **格式**：优先使用PNG格式，保证透明度和清晰度
- **分辨率**：最小宽度1200px，确保在高分辨率屏幕上清晰
- **色彩**：使用sRGB色彩空间，确保颜色一致性
- **压缩**：在保证质量的前提下适当压缩，单张图片不超过500KB

#### 3.2 截图规范
- **浏览器**：统一使用Chrome浏览器截图
- **窗口大小**：1920x1080分辨率，浏览器窗口最大化
- **界面主题**：使用系统默认主题，保持一致性
- **数据内容**：使用真实但脱敏的示例数据

#### 3.3 标注规范
- **标注工具**：使用统一的标注工具（如Snagit、Skitch）
- **标注颜色**：
  - 红色 (#FF0000) - 重要操作区域
  - 蓝色 (#0066CC) - 信息提示
  - 绿色 (#00AA00) - 成功状态
  - 橙色 (#FF9900) - 警告提示
- **标注样式**：
  - 边框：2px实线
  - 箭头：实心箭头，大小适中
  - 文字：12px字体，黑色或白色

### 4. 图片优化规范

#### 4.1 压缩优化
- **工具推荐**：TinyPNG、ImageOptim、Squoosh
- **压缩目标**：在保证视觉质量的前提下，文件大小减少30-50%
- **批量处理**：使用脚本或工具进行批量优化

#### 4.2 格式选择
- **PNG**：界面截图、图标、需要透明背景的图片
- **JPG**：照片、复杂图像、不需要透明背景的图片
- **WebP**：现代浏览器支持，文件更小（可选）
- **SVG**：矢量图标、简单图形

#### 4.3 响应式考虑
- **多尺寸**：为不同设备提供不同尺寸的图片（可选）
- **命名约定**：
  ```
  image-name.png          # 标准尺寸
  <EMAIL>       # 高分辨率版本
  image-name-mobile.png   # 移动端版本
  ```

### 5. 图片管理流程

#### 5.1 新增图片流程
1. **确定用途**：明确图片的使用场景和目的
2. **选择位置**：根据功能模块确定存储目录
3. **命名文件**：按照命名规范给图片命名
4. **优化处理**：压缩优化图片文件
5. **更新文档**：在对应文档中引用图片

#### 5.2 更新图片流程
1. **备份原图**：保留原图片的备份
2. **替换文件**：使用相同文件名替换
3. **检查引用**：确认所有引用该图片的文档
4. **测试验证**：检查图片显示是否正常

#### 5.3 删除图片流程
1. **检查引用**：确认没有文档引用该图片
2. **移动到临时目录**：先移动而不是直接删除
3. **观察期**：保留一段时间确认无问题
4. **最终删除**：确认无问题后彻底删除

### 6. 维护工具和脚本

#### 6.1 图片检查脚本
```bash
#!/bin/bash
# 检查未使用的图片文件
find src/assets -name "*.png" -o -name "*.jpg" | while read img; do
    if ! grep -r "$(basename "$img")" src/content/docs/ > /dev/null; then
        echo "未使用的图片: $img"
    fi
done
```

#### 6.2 命名检查脚本
```bash
#!/bin/bash
# 检查不符合命名规范的图片
find src/assets -name "*.png" -o -name "*.jpg" | while read img; do
    filename=$(basename "$img")
    if [[ ! $filename =~ ^[a-z0-9-]+\.(png|jpg)$ ]]; then
        echo "命名不规范: $img"
    fi
done
```

#### 6.3 图片优化脚本
```bash
#!/bin/bash
# 批量优化图片
find src/assets -name "*.png" | while read img; do
    echo "优化图片: $img"
    pngquant --quality=65-80 --ext .png --force "$img"
done
```

### 7. 实施计划

#### 阶段1：规范制定（已完成）
- 制定图片资源管理规范
- 设计标准目录结构
- 建立命名约定

#### 阶段2：现有资源整理（1-2小时）
1. 分析现有图片资源
2. 按新规范重新组织目录结构
3. 重命名不符合规范的文件
4. 删除冗余和未使用的图片

#### 阶段3：质量优化（1小时）
1. 批量优化图片文件大小
2. 统一图片格式和质量
3. 更新文档中的图片引用

#### 阶段4：工具部署（30分钟）
1. 部署图片检查脚本
2. 建立定期维护机制
3. 培训团队成员使用规范

### 8. 预期效果

1. **管理效率提升50%**：通过标准化的目录结构和命名规范
2. **文件大小减少30%**：通过图片优化和格式选择
3. **维护成本降低**：通过自动化工具和规范流程
4. **用户体验改善**：通过高质量、一致性的图片资源

---

*本规范将随着项目发展持续更新和完善*
