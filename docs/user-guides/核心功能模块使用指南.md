# 升辉ERP系统 - 核心功能模块使用指南

## 概述

本指南详细介绍升辉ERP系统的四大核心功能模块：商品管理、订单管理、客户管理和库存管理。这些模块构成了ERP系统的业务核心，掌握这些功能的使用方法对于高效运营企业至关重要。

## 一、商品管理模块

### 1.1 模块概述
商品管理是ERP系统的基础模块，负责管理企业的所有商品信息，包括商品基础资料、分类体系、价格管理等核心功能。

### 1.2 主要功能

#### 商品列表管理
- **功能描述**：集中管理所有商品的基础信息
- **访问路径**：商品管理 → 商品管理 → 商品列表
- **核心操作**：
  - 新增商品：录入商品基础信息、规格参数、图片等
  - 编辑商品：修改商品信息、更新商品状态
  - 批量操作：批量上架/下架、批量修改价格
  - 商品搜索：按名称、编码、分类等条件搜索

#### 商品分类管理
- **功能描述**：建立商品的层级分类体系
- **操作要点**：
  - 支持多级分类结构
  - 可设置分类图标和描述
  - 支持分类的拖拽排序
  - 分类删除前需确保无关联商品

#### 价格管理
- **功能描述**：管理商品的价格体系和调价流程
- **核心功能**：
  - 价格表：设置不同客户类型的价格
  - 调价单：批量调整商品价格
  - 价格历史：查看价格变动记录
  - 价格审核：重要价格变动需要审核

### 1.3 操作建议
1. **规范商品编码**：建立统一的商品编码规则
2. **完善商品信息**：确保商品图片、描述、规格等信息完整
3. **合理分类设计**：设计清晰的商品分类体系
4. **定期价格维护**：及时更新商品价格，保持价格准确性

## 二、订单管理模块

### 2.1 模块概述
订单管理是业务流程的核心环节，负责处理客户订单的全生命周期管理，从订单生成到交付完成的整个过程。

### 2.2 主要功能

#### 订货单管理
- **功能描述**：处理客户的购买订单
- **访问路径**：订单管理 → 订单管理 → 订货单
- **订单状态流程**：
  ```
  待确认 → 待发货 → 已发货 → 已完成
     ↓         ↓
   已取消    退款中 → 已退款
  ```

#### 核心操作流程
1. **订单确认**
   - 检查订单信息的准确性
   - 确认库存是否充足
   - 验证客户信用状况
   - 点击确认后订单状态变为"待发货"

2. **订单发货**
   - 填写物流公司信息
   - 录入运单号
   - 设置发货时间
   - 添加发货备注
   - 系统自动通知客户

3. **订单修改**
   - 仅限待发货状态的订单
   - 可修改商品数量、收货地址
   - 可添加或删除商品
   - 修改后需重新确认

#### 批量操作
- **批量发货**：选择多个订单批量处理发货
- **批量导出**：导出订单数据进行分析
- **批量打印**：打印发货单或配货单
- **批量状态修改**：批量修改订单状态

### 2.3 注意事项
1. **状态流转不可逆**：订单状态按固定流程流转
2. **库存影响**：确认订单会占用库存，取消订单会释放库存
3. **发货信息准确性**：确保物流信息准确，便于客户跟踪
4. **退款权限控制**：大额退款可能需要额外审批

## 三、客户管理模块

### 3.1 模块概述
客户管理是CRM的核心功能，负责管理客户的完整生命周期，从客户获取到客户维护的全过程管理。

### 3.2 主要功能

#### 客户列表管理
- **功能描述**：集中管理所有客户信息
- **访问路径**：客户管理 → 客户管理 → 客户列表
- **核心信息**：
  - 基本信息：姓名、联系方式、地址等
  - 业务信息：客户类型、所属业务员、信用额度
  - 状态信息：客户状态、注册时间、最后登录

#### 客户状态管理
客户状态流转规则：
```
新注册 → 待审核 → 审核结果
                    ↓
              通过 → 已启用 ⇄ 已禁用
                    ↓
              拒绝 → 审核拒绝 → 待审核
```

#### 客户搜索与筛选
- **快速搜索**：按客户名称、电话、编号搜索
- **高级筛选**：
  - 按业务员筛选
  - 按区域筛选（省-市-区三级）
  - 按客户分类筛选
  - 按状态筛选

#### 客户操作管理
1. **新增客户**
   - 填写必填信息：客户名称、登录账号、联系电话、客户类型、所在区域
   - 填写可选信息：客户标签、联系地址、备注信息
   - 后台新增的客户自动通过审核

2. **编辑客户**
   - 已启用客户需先禁用才能编辑
   - 关键信息修改可能需要重新审核
   - 系统记录所有修改历史

3. **批量操作**
   - 批量启用/禁用客户
   - 批量分配业务员
   - 批量导出客户数据

### 3.3 权限控制
- **查看权限**：客户管理-查看
- **新增权限**：客户管理-新增
- **编辑权限**：客户管理-编辑
- **删除权限**：客户管理-删除
- **批量操作权限**：客户管理-批量操作

## 四、库存管理模块

### 4.1 模块概述
库存管理是企业运营的重要环节，负责管理商品的库存数量、出入库流程、仓库配置等核心功能。

### 4.2 主要功能

#### 出入库管理
- **入库管理**：
  - 采购入库：采购商品到货入库
  - 退货入库：客户退货商品入库
  - 调拨入库：仓库间调拨入库
  - 其他入库：盘盈、赠品等其他入库

- **出库管理**：
  - 销售出库：客户订单商品出库
  - 调拨出库：仓库间调拨出库
  - 报损出库：商品损坏、过期等出库
  - 其他出库：样品、赠品等其他出库

#### 库存查询与监控
- **库存查询**：
  - 实时库存数量查询
  - 按商品、仓库、时间等维度查询
  - 支持库存预警设置
  - 库存成本核算

- **库存流水**：
  - 详细的出入库记录
  - 库存变动历史追踪
  - 批次管理和追溯
  - 保质期管理

#### 仓库管理
- **仓库配置**：
  - 多仓库管理
  - 库区和库位设置
  - 仓库权限分配
  - 仓库间调拨

- **库存盘点**：
  - 定期库存盘点
  - 盘点差异处理
  - 盘点报告生成
  - 库存调整记录

### 4.3 操作要点
1. **及时更新库存**：确保库存数据的实时性和准确性
2. **设置库存预警**：避免缺货或积压情况
3. **规范出入库流程**：确保每笔出入库都有完整记录
4. **定期库存盘点**：及时发现和处理库存差异

## 五、模块间协同

### 5.1 业务流程协同
各核心模块之间存在密切的业务关联：

```
商品管理 → 订单管理 → 库存管理 → 客户管理
    ↑                                    ↓
    ←←←←←←←←← 数据反馈 ←←←←←←←←←←←←←←←
```

### 5.2 数据流转
1. **商品到订单**：商品信息流转到订单中
2. **订单到库存**：订单确认后影响库存数量
3. **库存到采购**：库存不足触发采购需求
4. **客户到订单**：客户信息关联到订单中

### 5.3 权限协同
- 不同角色对各模块有不同的操作权限
- 跨模块操作需要相应的权限组合
- 重要操作可能需要多级审批

## 六、最佳实践建议

### 6.1 数据管理
1. **建立数据标准**：统一编码规则、命名规范
2. **定期数据清理**：清理无效、重复数据
3. **数据备份策略**：制定完善的数据备份计划

### 6.2 流程优化
1. **简化操作流程**：减少不必要的操作步骤
2. **自动化处理**：利用系统自动化功能提高效率
3. **异常处理机制**：建立完善的异常处理流程

### 6.3 用户培训
1. **分角色培训**：针对不同角色进行专项培训
2. **定期培训更新**：跟上系统功能更新
3. **建立操作手册**：制作详细的操作指导文档

### 6.4 系统维护
1. **定期系统检查**：检查系统运行状态
2. **性能监控**：监控系统性能指标
3. **及时更新升级**：保持系统版本更新

通过掌握这四大核心模块的使用方法，用户可以有效地管理企业的日常业务操作，提高工作效率，确保业务流程的顺畅运行。
