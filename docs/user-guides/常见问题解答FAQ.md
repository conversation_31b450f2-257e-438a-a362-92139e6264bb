# 升辉ERP系统 - 常见问题解答（FAQ）

## 概述

本文档汇总了用户在使用升辉ERP系统过程中经常遇到的问题和解决方案，按功能模块分类整理，帮助用户快速解决使用中的疑问。

## 一、系统登录和基础操作

### Q1: 忘记登录密码怎么办？
**A:** 
1. 联系系统管理员重置密码
2. 如果启用了手机验证，可以通过手机验证码重置
3. 系统管理员可以在"员工管理"中为用户重置密码

### Q2: 为什么登录后看不到某些功能菜单？
**A:** 
这是权限控制的结果，可能的原因：
1. 您的角色没有被分配相应的功能权限
2. 功能模块被系统管理员禁用
3. 您的账户状态异常
**解决方案：** 联系系统管理员检查和调整权限设置

### Q3: 系统运行缓慢怎么办？
**A:** 
1. 检查网络连接状态
2. 清理浏览器缓存和Cookie
3. 关闭不必要的浏览器标签页
4. 检查电脑内存使用情况
5. 如果问题持续，联系技术支持

### Q4: 如何修改个人信息？
**A:** 
1. 点击右上角的用户头像
2. 选择"个人设置"或"个人中心"
3. 修改相关信息后保存
4. 部分信息可能需要管理员审核

## 二、商品管理相关问题

### Q5: 如何批量导入商品信息？
**A:** 
1. 下载商品导入模板
2. 按照模板格式填写商品信息
3. 在商品管理页面选择"批量导入"
4. 上传填写好的Excel文件
5. 检查导入结果，处理错误信息

### Q6: 商品图片上传失败怎么办？
**A:** 
可能的原因和解决方案：
1. **图片格式不支持**：确保使用JPG、PNG、GIF格式
2. **图片尺寸过大**：建议图片大小不超过2MB
3. **网络问题**：检查网络连接，重新上传
4. **服务器存储空间不足**：联系系统管理员

### Q7: 为什么无法修改已上架的商品？
**A:** 
这是为了保护数据一致性：
1. 已上架商品需要先下架才能编辑
2. 或者使用"商品修改申请"功能
3. 重要信息修改可能需要审核流程

### Q8: 商品分类删除时提示"存在关联商品"？
**A:** 
1. 先将该分类下的所有商品移动到其他分类
2. 或者删除该分类下的所有商品
3. 确认分类下没有子分类
4. 然后才能删除该分类

## 三、订单管理相关问题

### Q9: 订单状态为什么不能随意修改？
**A:** 
订单状态有固定的流转规则：
```
待确认 → 待发货 → 已发货 → 已完成
   ↓         ↓
已取消    退款中 → 已退款
```
这样设计是为了保证业务流程的规范性和数据的一致性。

### Q10: 如何处理客户要求修改已确认的订单？
**A:** 
1. **订单未发货**：可以直接修改订单信息
2. **订单已发货**：需要通过退货流程处理
3. **修改收货地址**：联系物流公司修改配送地址
4. **修改商品**：可能需要部分退款或补款

### Q11: 批量发货操作失败怎么办？
**A:** 
1. 检查选中的订单是否都是"待发货"状态
2. 确认物流信息填写完整和正确
3. 检查是否有库存不足的商品
4. 查看具体的错误提示信息
5. 对失败的订单单独处理

### Q12: 如何查找特定时间段的订单？
**A:** 
1. 在订单列表页面使用时间筛选功能
2. 可以选择预设的时间范围（今天、昨天、本周、本月）
3. 也可以自定义时间范围
4. 结合其他筛选条件精确查找

## 四、客户管理相关问题

### Q13: 为什么无法编辑某个客户的信息？
**A:** 
可能的原因：
1. **客户状态为"已启用"**：需要先禁用才能编辑
2. **权限不足**：没有客户编辑权限
3. **客户正在被其他用户编辑**：等待其他用户完成编辑
4. **数据锁定**：客户数据被系统锁定

### Q14: 客户状态为什么会自动变化？
**A:** 
系统可能根据以下规则自动调整客户状态：
1. **长期未登录**：超过设定时间未登录自动禁用
2. **信用额度超限**：欠款超过信用额度自动禁用
3. **异常行为**：检测到异常行为自动禁用
4. **批量操作**：管理员执行批量状态修改

### Q15: 如何恢复误删的客户？
**A:** 
客户删除操作通常不可恢复，建议：
1. **预防措施**：删除前仔细确认
2. **使用禁用**：对于不需要的客户使用禁用而不是删除
3. **数据备份**：定期备份客户数据
4. **联系技术支持**：如果有数据备份，可能可以恢复

### Q16: 如何批量导入客户信息？
**A:** 
1. 下载客户导入模板
2. 按照模板格式填写客户信息
3. 注意必填字段和数据格式要求
4. 使用批量导入功能上传文件
5. 检查导入结果，处理错误记录

## 五、库存管理相关问题

### Q17: 库存数据不准确怎么办？
**A:** 
1. **立即盘点**：对相关商品进行库存盘点
2. **检查出入库记录**：查看最近的出入库流水
3. **核对订单状态**：确认订单状态与库存变动的一致性
4. **系统同步**：检查是否有数据同步延迟
5. **联系技术支持**：如果问题持续存在

### Q18: 如何设置库存预警？
**A:** 
1. 在商品管理中设置每个商品的最低库存量
2. 系统会在库存低于预警值时自动提醒
3. 可以设置预警通知的接收人
4. 定期检查预警设置的合理性

### Q19: 出入库单据能否修改或删除？
**A:** 
1. **未审核单据**：可以修改或删除
2. **已审核单据**：一般不能修改，需要通过冲销单据处理
3. **特殊情况**：联系系统管理员处理
4. **操作记录**：所有操作都会留下审计记录

### Q20: 多仓库之间如何调拨商品？
**A:** 
1. 创建调拨单，选择调出和调入仓库
2. 添加需要调拨的商品和数量
3. 提交审核（如果需要）
4. 调出仓库执行出库操作
5. 调入仓库执行入库操作

## 六、财务管理相关问题

### Q21: 应收账款数据与实际不符怎么办？
**A:** 
1. **核对订单数据**：检查相关订单的状态和金额
2. **检查收款记录**：确认收款单据是否正确录入
3. **查看调整记录**：检查是否有手工调整记录
4. **联系财务人员**：进行详细的账务核对
5. **系统对账**：使用系统对账功能进行核对

### Q22: 如何处理客户的部分付款？
**A:** 
1. 在收款单中录入实际收款金额
2. 系统会自动计算剩余应收金额
3. 可以设置分期收款计划
4. 跟踪后续收款情况

### Q23: 财务报表数据异常怎么办？
**A:** 
1. **检查数据源**：确认基础数据的准确性
2. **核对时间范围**：检查报表的查询条件
3. **权限确认**：确认是否有查看相关数据的权限
4. **刷新数据**：尝试刷新或重新生成报表
5. **联系财务人员**：进行专业的财务分析

## 七、系统配置相关问题

### Q24: 如何添加新的用户角色？
**A:** 
1. 进入"系统设置" → "组织管理" → "角色管理"
2. 点击"新增角色"
3. 设置角色名称和描述
4. 分配相应的功能权限
5. 设置数据权限范围
6. 保存角色配置

### Q25: 小程序配置后无法正常使用？
**A:** 
1. **检查接口配置**：确认微信API接口配置正确
2. **网络连接**：检查服务器网络连接
3. **权限设置**：确认小程序权限配置
4. **版本问题**：检查小程序版本是否最新
5. **联系技术支持**：提供详细的错误信息

### Q26: 如何备份系统数据？
**A:** 
1. **自动备份**：系统通常有自动备份功能
2. **手动备份**：在系统管理中执行手动备份
3. **数据导出**：使用数据导出功能备份重要数据
4. **定期检查**：定期检查备份文件的完整性
5. **异地存储**：重要备份文件建议异地存储

## 八、技术支持和联系方式

### 获取帮助的途径
1. **在线帮助文档**：查阅系统内置的帮助文档
2. **在线客服**：使用系统内的在线客服功能
3. **技术支持热线**：拨打技术支持电话
4. **邮件支持**：发送邮件到技术支持邮箱
5. **远程协助**：申请远程技术支持服务

### 提交问题时请提供
1. **详细的问题描述**：包括操作步骤和错误现象
2. **系统环境信息**：浏览器版本、操作系统等
3. **错误截图**：如果有错误提示，请提供截图
4. **用户信息**：用户名、角色、权限等信息
5. **联系方式**：便于技术人员联系的电话或邮箱

### 紧急问题处理
对于影响业务正常运行的紧急问题：
1. **立即联系**：通过电话联系技术支持
2. **详细描述**：清楚描述问题的紧急程度和影响范围
3. **配合处理**：积极配合技术人员进行问题排查
4. **记录过程**：记录问题处理过程，便于后续分析

## 总结

本FAQ文档涵盖了升辉ERP系统使用过程中的常见问题，建议用户：
1. **熟悉基础操作**：掌握系统的基本操作方法
2. **了解业务流程**：理解系统的业务逻辑和流程规则
3. **定期培训学习**：参加系统培训，提升使用技能
4. **及时寻求帮助**：遇到问题及时联系技术支持

如果您的问题在本文档中没有找到答案，请联系我们的技术支持团队，我们将竭诚为您服务。
