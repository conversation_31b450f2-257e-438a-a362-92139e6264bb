# 升辉ERP帮助文档写作规范与模板

## 文档写作规范

### 1. 文档结构规范

#### 1.1 Frontmatter 规范
每个文档必须包含以下frontmatter信息：

```yaml
---
title: 功能名称
description: 功能的简要描述（50-100字）
tags: [标签1, 标签2, 标签3]  # 可选，用于搜索优化
sidebar:
  order: 1  # 可选，用于排序
---
```

#### 1.2 标题层级规范
- **H1 (#)**：页面主标题，与frontmatter中的title一致
- **H2 (##)**：主要功能模块
- **H3 (###)**：具体操作步骤或子功能
- **H4 (####)**：详细说明或注意事项
- **H5及以下**：避免使用，保持结构简洁

#### 1.3 标准文档结构
```markdown
# 功能名称

功能的简要介绍和作用说明（1-2段）

## 功能概述

![功能主界面截图](/src/assets/module/feature-main.png)

功能的整体介绍，包括：
- 主要用途
- 核心功能点
- 适用场景

## 操作指南

### 基本操作

#### 1. 操作名称

操作步骤：
1. 第一步操作
2. 第二步操作
3. 第三步操作

![操作截图](/src/assets/module/feature-step1.png)

#### 2. 高级操作

详细的操作说明...

## 功能详解

### 界面元素说明

| 元素名称 | 功能说明 | 备注 |
|---------|---------|------|
| 按钮A | 执行操作A | 需要权限 |
| 字段B | 输入信息B | 必填项 |

### 字段说明

详细的字段含义和填写要求...

## 注意事项

1. 重要提醒事项
2. 常见错误避免
3. 权限要求说明

## 常见问题

### Q: 常见问题1？
A: 详细解答...

### Q: 常见问题2？
A: 详细解答...

## 相关功能

- [相关功能1](../related/function1)
- [相关功能2](../related/function2)
```

### 2. 写作风格规范

#### 2.1 语言风格
- **简洁明了**：使用简单直接的语言，避免冗长复杂的句子
- **用户导向**：从用户角度描述功能和操作
- **统一术语**：使用系统中的标准术语，保持一致性
- **友好语调**：使用"您"而不是"你"，保持专业友好的语调

#### 2.2 操作描述规范
- **动作明确**：使用"点击"、"选择"、"输入"等明确的动作词
- **位置准确**：准确描述界面元素的位置，如"右上角"、"左侧菜单"
- **步骤清晰**：使用有序列表描述操作步骤
- **结果说明**：说明操作后的预期结果

#### 2.3 格式规范
- **按钮名称**：使用引号标识，如点击"保存"按钮
- **菜单路径**：使用箭头连接，如"系统设置" → "用户管理"
- **字段名称**：使用引号标识，如在"用户名"字段中输入
- **状态标识**：使用特定格式，如【已审核】、【待处理】

### 3. 图片使用规范

#### 3.1 图片命名规范
```
/src/assets/[模块名]/[功能名]-[用途].png

示例：
/src/assets/goods/base-data-list-main.png        # 主界面
/src/assets/goods/base-data-list-add-form.png    # 添加表单
/src/assets/goods/base-data-list-edit-dialog.png # 编辑对话框
```

#### 3.2 图片质量要求
- **分辨率**：最小1200px宽度，保证清晰度
- **格式**：优先使用PNG格式，保证透明度和清晰度
- **大小**：单张图片不超过500KB，必要时进行压缩
- **标注**：重要操作区域使用红框或箭头标注

#### 3.3 截图规范（重点要求）

##### 3.3.1 截图内容要求
- **界面完整**：截图包含完整的功能界面，确保用户能看到完整的操作环境
- **数据真实**：使用真实但脱敏的示例数据，避免空白或测试数据
- **突出重点**：对关键操作区域进行标注，使用红框、箭头、数字标记等
- **统一风格**：保持截图的浏览器、主题风格一致

##### 3.3.2 截图技术标准
- **分辨率要求**：最小1200px宽度，确保在不同设备上的可读性
- **格式选择**：优先使用PNG格式，保证图片质量和透明度支持
- **文件大小**：单张截图不超过500KB，使用适当的压缩工具优化
- **色彩准确**：确保截图色彩与实际界面一致，避免色差

##### 3.3.3 截图标注规范
- **标注工具**：使用红色边框（#FF0000，2-3px宽度）标注关键区域
- **箭头指示**：使用红色箭头（#FF0000）指向具体操作位置
- **数字标记**：对多步骤操作使用数字标记（1、2、3...），红色圆圈白色数字
- **文字说明**：必要时添加简短的文字说明，使用红色字体（#FF0000）

##### 3.3.4 截图组织要求
- **按步骤排列**：截图必须按照操作步骤的顺序排列
- **逻辑连贯**：确保截图之间的逻辑关系清晰，操作流程完整
- **内容一致**：截图内容必须与文档描述保持完全一致
- **及时更新**：当系统界面更新时，及时更新对应的截图

##### 3.3.5 特殊场景截图要求
- **表单填写**：展示表单的完整填写过程，包括必填项标识
- **列表操作**：展示列表的筛选、排序、批量操作等功能
- **弹窗对话框**：确保弹窗内容完整可见，包括按钮和选项
- **状态变化**：展示操作前后的状态变化，如审核前后的状态对比
- **错误提示**：包含常见错误提示和解决方案的截图

### 4. 链接和引用规范

#### 4.1 内部链接
```markdown
# 相对路径链接
[商品分类管理](../manage/goods-classify)

# 绝对路径链接（不推荐）
[商品分类管理](/goods/manage/goods-classify)
```

#### 4.2 外部链接
```markdown
# 在新窗口打开
[升辉官网](https://www.byshun.cn/){:target="_blank"}
```

#### 4.3 图片引用
```markdown
# 标准图片引用
![图片描述](/src/assets/module/image-name.png)

# 带标题的图片
![图片描述](/src/assets/module/image-name.png "图片标题")
```

### 5. 表格使用规范

#### 5.1 标准表格格式
```markdown
| 列标题1 | 列标题2 | 列标题3 |
|---------|---------|---------|
| 内容1   | 内容2   | 内容3   |
| 内容4   | 内容5   | 内容6   |
```

#### 5.2 表格使用场景
- **字段说明**：列出界面字段的含义和要求
- **功能对比**：对比不同功能或选项的差异
- **参数列表**：列出API参数或配置参数
- **状态说明**：说明不同状态的含义

### 6. 特殊组件使用

#### 6.1 提示框组件
```markdown
:::note
这是一般提示信息
:::

:::tip
这是有用的小贴士
:::

:::caution
这是需要注意的事项
:::

:::danger
这是重要警告信息
:::
```

#### 6.2 代码块
```markdown
# 带语言标识的代码块
```javascript
// JavaScript代码示例
function example() {
    return "Hello World";
}
```

# 带文件名的代码块
```json title="config.json"
{
    "name": "升辉ERP",
    "version": "1.0.0"
}
```
```

### 7. SEO优化规范

#### 7.1 关键词优化
- 在标题中包含核心关键词
- 在描述中自然使用相关术语
- 在正文中适当重复关键概念

#### 7.2 元数据优化
```yaml
---
title: 商品资料管理 - 升辉ERP帮助文档
description: 详细介绍升辉ERP系统中商品资料的创建、编辑、管理等功能操作指南
tags: [商品管理, 商品资料, 商品信息, ERP系统]
---
```

## 文档模板

### 模板1：功能操作类文档

适用于具体功能的操作指南，如商品管理、订单处理等。

```markdown
---
title: [功能名称]
description: [功能的简要描述，说明主要用途和价值]
tags: [相关标签]
---

# [功能名称]

[功能的简要介绍，1-2段文字说明功能的作用和重要性]

## 功能概述

![功能主界面](/src/assets/[模块]/[功能]-main.png)

[功能的整体介绍，包括：]
- 主要用途和应用场景
- 核心功能点列表
- 与其他功能的关联关系

## 操作指南

### [主要操作1]

#### 操作步骤

1. [第一步操作描述]
2. [第二步操作描述]
3. [第三步操作描述]

![操作截图](/src/assets/[模块]/[功能]-step1.png)

#### 注意事项

- [重要提醒1]
- [重要提醒2]

### [主要操作2]

[详细的操作说明...]

## 界面说明

### 主要功能区域

[描述界面的主要区域和功能]

### 字段说明

| 字段名称 | 字段类型 | 是否必填 | 说明 |
|---------|---------|---------|------|
| [字段1] | [类型] | 是/否 | [详细说明] |
| [字段2] | [类型] | 是/否 | [详细说明] |

## 常见问题

### Q: [常见问题1]？
A: [详细解答]

### Q: [常见问题2]？
A: [详细解答]

## 相关功能

- [[相关功能1]](../path/to/function1)
- [[相关功能2]](../path/to/function2)
```

### 模板2：概念说明类文档

适用于系统概念、术语解释等内容。

```markdown
---
title: [概念名称]
description: [概念的定义和重要性说明]
tags: [相关标签]
---

# [概念名称]

## 定义

[概念的准确定义]

## 重要性

[说明这个概念在系统中的重要性和作用]

## 详细说明

### [方面1]
[详细说明]

### [方面2]
[详细说明]

## 实际应用

[举例说明概念在实际业务中的应用]

## 相关概念

- [[相关概念1]](../path/to/concept1)
- [[相关概念2]](../path/to/concept2)
```

### 模板3：故障排除类文档

适用于问题解决和故障排除指南。

```markdown
---
title: [问题类型] - 故障排除
description: [问题的简要描述和解决方案概述]
tags: [故障排除, 问题解决, 相关功能标签]
---

# [问题类型] - 故障排除

## 问题描述

[详细描述问题的表现和影响]

## 可能原因

1. [原因1]
2. [原因2]
3. [原因3]

## 解决方案

### 方案1：[解决方案名称]

**适用情况**：[什么情况下使用这个方案]

**操作步骤**：
1. [步骤1]
2. [步骤2]
3. [步骤3]

### 方案2：[解决方案名称]

[详细说明...]

## 预防措施

[如何避免类似问题再次发生]

## 联系支持

如果以上方案都无法解决问题，请联系技术支持：
- 技术支持电话：[电话号码]
- 技术支持邮箱：[邮箱地址]
```

---

*本规范将随着文档建设的进展持续更新和完善*
